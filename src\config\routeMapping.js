// routeMapping.js
export const getRouteMappingData = (tenantInfo) => {
  const {
    inventory_config,
    purchase_config,
    sales_config,
    production_config,
    quality_control_config,
    global_config,
    logistics_config,
  } = tenantInfo || {};

  return {
    // -->Inventory
    // Product
    '/inventory/manage': {
      isRestricted: !inventory_config?.sub_modules?.product?.is_active,
      moduleName: 'Inventory Management',
      isForm: false,
    },
    '/inventory/product/view/:productSkuId': {
      isRestricted: !inventory_config?.sub_modules?.product?.is_active,
      moduleName: 'Inventory Management',
      isForm: false,
    },
    // Stock Transfer
    '/inventory/stock-transfer': {
      isRestricted: !inventory_config?.sub_modules?.stock_transfer?.is_active,
      moduleName: 'Stock Transfer',
      isForm: false,
    },
    '/inventory/stock-transfer/view/:stockTransferId': {
      isRestricted: !inventory_config?.sub_modules?.stock_transfer?.is_active,
      moduleName: 'Stock Transfer',
      isForm: false,
    },
    '/inventory/stock-transfer/create': {
      isRestricted: !inventory_config?.sub_modules?.stock_transfer?.is_active,
      moduleName: 'Stock Transfer',
      isForm: true,
    },
    '/inventory/stock-transfer/update/:stockTransferId': {
      isRestricted: !inventory_config?.sub_modules?.stock_transfer?.is_active,
      moduleName: 'Stock Transfer',
      isForm: true,
    },
    // Stock Adjustment
    '/inventory/indent': {
      isRestricted: !inventory_config?.sub_modules?.stock_adjustment?.is_active,
      moduleName: 'Stock Adjustment',
      isForm: false,
    },
    '/inventory/indent/create': {
      isRestricted: !inventory_config?.sub_modules?.stock_adjustment?.is_active,
      moduleName: 'Stock Adjustment',
      isForm: true,
    },
    // Delivery Challan
    '/inventory/delivery-challan': {
      isRestricted: !inventory_config?.sub_modules?.delivery_challan?.is_active,
      moduleName: 'Delivery Challan',
      isForm: false,
    },
    '/inventory/delivery-challan/view/:dcId': {
      isRestricted: !inventory_config?.sub_modules?.delivery_challan?.is_active,
      moduleName: 'Delivery Challan',
      isForm: false,
    },
    '/inventory/delivery-challan/create': {
      isRestricted: !inventory_config?.sub_modules?.delivery_challan?.is_active,
      moduleName: 'Delivery Challan',
      isForm: true,
    },

    // --> Purchase
    // Vendor
    '/purchase/vendors/manage': {
      isRestricted: !purchase_config?.sub_modules?.vendor?.is_active,
      moduleName: 'Vendor',
      isForm: false,
    },
    '/vendors/view/:vendorId': {
      isRestricted: !purchase_config?.sub_modules?.vendor?.is_active,
      moduleName: 'Vendor',
      isForm: false,
    },
    // Purchase Request
    '/purchase/purchase-request': {
      isRestricted: !purchase_config?.sub_modules?.purchase_request?.is_active,
      moduleName: 'Material Request',
      isForm: false,
    },
    '/purchase/purchase-request/view/:prId': {
      isRestricted: !purchase_config?.sub_modules?.purchase_request?.is_active,
      moduleName: 'Material Request',
      isForm: false,
    },
    '/purchase/purchase-request/create': {
      isRestricted: !purchase_config?.sub_modules?.purchase_request?.is_active,
      moduleName: 'Material Request',
      isForm: true,
    },
    '/purchase/purchase-request/update/:prId': {
      isRestricted: !purchase_config?.sub_modules?.purchase_request?.is_active,
      moduleName: 'Material Request',
      isForm: true,
    },
    // Purchase Indent
    '/purchase/purchase-indent': {
      isRestricted: !purchase_config?.sub_modules?.purchase_indent?.is_active,
      moduleName: 'Purchase Indent',
      isForm: false,
    },
    '/purchase/purchase-indent/view/:piId': {
      isRestricted: !purchase_config?.sub_modules?.purchase_indent?.is_active,
      moduleName: 'Purchase Indent',
      isForm: false,
    },
    '/purchase/purchase-indent/create': {
      isRestricted: !purchase_config?.sub_modules?.purchase_indent?.is_active,
      moduleName: 'Purchase Indent',
      isForm: true,
    },
    '/purchase/purchase-indent/update/:piId': {
      isRestricted: !purchase_config?.sub_modules?.purchase_indent?.is_active,
      moduleName: 'Purchase Indent',
      isForm: true,
    },
    // Purchase Order
    '/purchase/purchase-orders': {
      isRestricted: !purchase_config?.sub_modules?.purchase_order?.is_active,
      moduleName: 'Purchase Order',
      isForm: false,
    },
    '/approval': {
      isRestricted: !purchase_config?.sub_modules?.purchase_order?.is_active,
      moduleName: 'Purchase Order',
      isForm: false,
    },
    '/purchase/purchase-orders/create': {
      isRestricted: !purchase_config?.sub_modules?.purchase_order?.is_active,
      moduleName: 'Purchase Order',
      isForm: true,
    },
    '/purchase/purchase-orders/update/:poId': {
      isRestricted: !purchase_config?.sub_modules?.purchase_order?.is_active,
      moduleName: 'Purchase Order',
      isForm: true,
    },
    // Request For Quaotation
    '/purchase/rfq': {
      isRestricted: !purchase_config?.addons?.request_for_quotation?.is_active,
      moduleName: 'Request For Quotation',
      isForm: false,
    },
    '/purchase/rfq/create': {
      isRestricted: !purchase_config?.addons?.request_for_quotation?.is_active,
      moduleName: 'Request For Quotation',
      isForm: true,
    },
    '/purchase/rfq/update/:rfqId': {
      isRestricted: !purchase_config?.addons?.request_for_quotation?.is_active,
      moduleName: 'Request For Quotation',
      isForm: true,
    },
    // Goods Receving
    '/purchase/goods-receiving': {
      isRestricted: !purchase_config?.sub_modules?.goods_receiving_note?.is_active,
      moduleName: 'Goods Receiving Notes',
      isForm: false,
    },
    '/purchase/goods-receiving/view/:grnId': {
      isRestricted: !purchase_config?.sub_modules?.goods_receiving_note?.is_active,
      moduleName: 'Goods Receiving Notes',
      isForm: false,
    },
    '/purchase/goods-receiving/view/:grnId/:type': {
      isRestricted: !purchase_config?.sub_modules?.goods_receiving_note?.is_active,
      moduleName: 'Goods Receiving Notes',
      isForm: false,
    },
    '/purchase/goods-receiving/create': {
      isRestricted: !purchase_config?.sub_modules?.goods_receiving_note?.is_active,
      moduleName: 'Goods Receiving Notes',
      isForm: true,
    },
    '/purchase/goods-receiving/update/:grnId': {
      isRestricted: !purchase_config?.sub_modules?.goods_receiving_note?.is_active,
      moduleName: 'Goods Receiving Notes',
      isForm: true,
    },
    '/purchase/goods-receiving/update/:grnId/:type': {
      isRestricted: !purchase_config?.sub_modules?.goods_receiving_note?.is_active,
      moduleName: 'Goods Receiving Notes',
      isForm: true,
    },
    // Account Payable Invoice
    '/purchase/account-payable-invoice': {
      isRestricted: !purchase_config?.sub_modules?.account_payable_invoice?.is_active,
      moduleName: 'Account Payable Invoice',
      isForm: false,
    },
    '/purchase/account-payable-invoice/view/:apiId': {
      isRestricted: !purchase_config?.sub_modules?.account_payable_invoice?.is_active,
      moduleName: 'Account Payable Invoice',
      isForm: false,
    },
    '/purchase/account-payable-invoice/view/:apiId': {
      isRestricted: !purchase_config?.sub_modules?.account_payable_invoice?.is_active,
      moduleName: 'Account Payable Invoice',
      isForm: false,
    },
    '/purchase/account-payable-invoice/create': {
      isRestricted: !purchase_config?.sub_modules?.account_payable_invoice?.is_active,
      moduleName: 'Account Payable Invoice',
      isForm: true,
    },
    '/purchase/account-payable-invoice/update/:apiId': {
      isRestricted: !purchase_config?.sub_modules?.account_payable_invoice?.is_active,
      moduleName: 'Account Payable Invoice',
      isForm: true,
    },
    '/purchase/account-payable-invoice/update/:apiId': {
      isRestricted: !purchase_config?.sub_modules?.account_payable_invoice?.is_active,
      moduleName: 'Account Payable Invoice',
      isForm: true,
    },
    // Debit Note
    '/purchase/debit-notes': {
      isRestricted: !purchase_config?.sub_modules?.debit_note?.is_active,
      moduleName: 'Debit Note',
      isForm: false,
    },
    '/purchase/debit-note/view/:debitNoteId': {
      isRestricted: !purchase_config?.sub_modules?.debit_note?.is_active,
      moduleName: 'Debit Note',
      isForm: false,
    },
    '/purchase/debit-notes/create': {
      isRestricted: !purchase_config?.sub_modules?.debit_note?.is_active,
      moduleName: 'Debit Note',
      isForm: true,
    },
    '/purchase/debit-notes/update/:debitNoteId': {
      isRestricted: !purchase_config?.sub_modules?.debit_note?.is_active,
      moduleName: 'Debit Note',
      isForm: true,
    },
    // Outgoing Payments
    '/purchase/vendor-payouts': {
      isRestricted: !purchase_config?.sub_modules?.payment_outgoing?.is_active,
      moduleName: 'Outgoing Payments',
      isForm: false,
    },
    '/payments/create': {
      isRestricted: !purchase_config?.sub_modules?.payment_outgoing?.is_active,
      moduleName: 'Outgoing Payments',
      isForm: true,
    },
    // Expense
    '/purchase/expense': {
      isRestricted: !purchase_config?.addons?.expense?.is_active,
      moduleName: 'Expense',
      isForm: false,
    },
    '/purchase/expense/view/:expenseId': {
      isRestricted: !purchase_config?.addons?.expense?.is_active,
      moduleName: 'Expense',
      isForm: false,
    },

    // --> Production
    // Bill Of Material
    '/production/bom': {
      isRestricted: !production_config?.sub_modules?.bill_of_material?.is_active,
      moduleName: 'Bill Of Material',
      isForm: false,
    },
    // Manufacturing Order
    '/production/manufacturing-orders': {
      isRestricted: !production_config?.sub_modules?.manufacturing_order?.is_active,
      moduleName: 'Manufacturing Orders',
      isForm: false,
    },
    '/production/manufacturing-orders/view/:moId': {
      isRestricted: !production_config?.sub_modules?.manufacturing_order?.is_active,
      moduleName: 'Manufacturing Orders',
      isForm: false,
    },
    '/production/manufacturing-orders/create': {
      isRestricted: !production_config?.sub_modules?.manufacturing_order?.is_active,
      moduleName: 'Manufacturing Orders',
      isForm: true,
    },
    '/production/manufacturing-orders/update/:moId': {
      isRestricted: !production_config?.sub_modules?.manufacturing_order?.is_active,
      moduleName: 'Manufacturing Orders',
      isForm: true,
    },
    // Job Cards
    '/production/job-works': {
      isRestricted: !production_config?.addons?.factory_setup?.is_active,
      moduleName: 'Job Cards',
      isForm: false,
    },
    // Materials Planning
    '/production/planning': {
      isRestricted: !production_config?.addons?.material_planing?.is_active,
      moduleName: 'Materials Planing',
      isForm: false,
    },
    '/production/planning/view/:mrpId': {
      isRestricted: !production_config?.addons?.material_planing?.is_active,
      moduleName: 'Materials Planing',
      isForm: false,
    },
    // Materials Planning v2
    '/production/planning/v2': {
      isRestricted: !production_config?.addons?.material_planing?.is_active,
      moduleName: 'Materials Planing',
      isForm: false,
    },
    // Create Materials Planning v2
    '/production/planning/v2/create': {
      isRestricted: !production_config?.addons?.material_planing?.is_active,
      moduleName: 'Manufacturing Orders',
      isForm: true,
    },
    // View Materials Planning v2
    '/production/planning/v2/view/:mrpId': {
      isRestricted: !production_config?.addons?.material_planing?.is_active,
      moduleName: 'Manufacturing Orders',
      isForm: false,
    },

    // Factory Setup
    '/production/configurations': {
      isRestricted: !production_config?.addons?.factory_setup?.is_active,
      moduleName: 'Factory Setup',
      isForm: false,
    },
    // --> Quality Control
    // Qualit Control
    '/quality/quality-control-points': {
      isRestricted: !quality_control_config?.sub_modules?.quality_control_rule?.is_active,
      moduleName: 'Quality Control Rules',
      isForm: false,
    },
    // Quality Checks
    '/quality/quality-checks': {
      isRestricted: !quality_control_config?.sub_modules?.quality_check?.is_active,
      moduleName: 'Quality Checks',
      isForm: false,
    },
    // --> Sales
    // Customer
    '/sales/customers': {
      isRestricted: !sales_config?.sub_modules?.customer?.is_active,
      moduleName: 'Customers',
      isForm: false,
    },
    '/sales/customer/view/:customerId': {
      isRestricted: !sales_config?.sub_modules?.customer?.is_active,
      moduleName: 'Customers',
      isForm: false,
    },
    // Sales Estimate
    '/sales/estimate': {
      isRestricted: !sales_config?.sub_modules?.sales_estimate?.is_active,
      moduleName: 'Estimate',
      isForm: false,
    },
    '/sales/estimate/view/:orderId': {
      isRestricted: !sales_config?.sub_modules?.sales_estimate?.is_active,
      moduleName: 'Estimate',
      isForm: false,
    },
    '/sales/estimate/create': {
      isRestricted: !sales_config?.sub_modules?.sales_estimate?.is_active,
      moduleName: 'Estimate',
      isForm: true,
    },
    '/sales/estimate/update/:orderId': {
      isRestricted: !sales_config?.sub_modules?.sales_estimate?.is_active,
      moduleName: 'Estimate',
      isForm: true,
    },
    // Sales Order
    '/sales/sales-orders': {
      isRestricted: !sales_config?.sub_modules?.sales_order?.is_active && !sales_config?.sub_modules?.annual_maintenance_contract?.is_active,
      moduleName: 'Sales Order',
      isForm: false,
    },
    '/sales/sales-order/view/:orderId': {
      isRestricted: !sales_config?.sub_modules?.sales_order?.is_active,
      moduleName: 'Sales Order',
      isForm: false,
    },
    '/sales/sales-orders/create': {
      isRestricted: !sales_config?.sub_modules?.sales_order?.is_active && !sales_config?.sub_modules?.annual_maintenance_contract?.is_active,
      moduleName: 'Sales Order',
      isForm: true,
    },
    '/sales/sales-orders/update/:orderId': {
      isRestricted: !sales_config?.sub_modules?.sales_order?.is_active && !sales_config?.sub_modules?.annual_maintenance_contract?.is_active,
      moduleName: 'Sales Order',
      isForm: true,
    },
    //Packing Slip
    '/sales/packing-slips': {
      isRestricted: !sales_config?.sub_modules?.packing_slip?.is_active,
      moduleName: 'Packing Slip',
      isForm: false,
    },
    '/sales/packing-slip/view/:orderId': {
      isRestricted: !sales_config?.sub_modules?.packing_slip?.is_active,
      moduleName: 'Packing Slip',
      isForm: false,
    },
    '/sales/packing-slips/create': {
      isRestricted: !sales_config?.sub_modules?.packing_slip?.is_active,
      moduleName: 'Packing Slip',
      isForm: true,
    },
    '/sales/packing-slip/update/:orderId': {
      isRestricted: !sales_config?.sub_modules?.packing_slip?.is_active,
      moduleName: 'Packing Slip',
      isForm: true,
    },
    // Invoice
    '/sales/invoices': {
      isRestricted: !sales_config?.sub_modules?.invoice?.is_active,
      moduleName: 'Invoice',
      isForm: false,
    },
    '/sales/invoice/view/:invoiceId': {
      isRestricted: !sales_config?.sub_modules?.invoice?.is_active,
      moduleName: 'Invoice',
      isForm: false,
    },
    '/sales/invoices/create': {
      isRestricted: !sales_config?.sub_modules?.invoice?.is_active,
      moduleName: 'Invoice',
      isForm: true,
    },
    // TODO update permission here for consumption
     // Consumption
     '/sales/consumption': {
      isRestricted: !sales_config?.sub_modules?.consumption_order?.is_active,
      moduleName: 'Consumption Order',
      isForm: false,
    },
    '/sales/consumption/view/:consumptionId': {
      isRestricted: !sales_config?.sub_modules?.consumption_order?.is_active,
      moduleName: 'Consumption Order',
      isForm: false,
    },
    '/sales/consumption/create': {
      isRestricted: !sales_config?.sub_modules?.consumption_order?.is_active,
      moduleName: 'Consumption Order',
      isForm: true,
    },
    '/sales/consumption/update/:consumptionId': {
      isRestricted: !sales_config?.sub_modules?.consumption_order?.is_active,
      moduleName: 'Consumption Order',
      isForm: true,
    },
    // Credit Note
    '/sales/credit-notes': {
      isRestricted: !sales_config?.sub_modules?.credit_note?.is_active,
      moduleName: 'Credit Note',
      isForm: false,
    },
    '/sales/credit-notes/view/:creditNoteId': {
      isRestricted: !sales_config?.sub_modules?.credit_note?.is_active,
      moduleName: 'Credit Note',
      isForm: false,
    },
    '/sales/credit-notes/create': {
      isRestricted: !sales_config?.sub_modules?.credit_note?.is_active,
      moduleName: 'Credit Note',
      isForm: true,
    },
    '/sales/credit-notes/update/:creditNoteId': {
      isRestricted: !sales_config?.sub_modules?.credit_note?.is_active,
      moduleName: 'Credit Note',
      isForm: true,
    },
    // Return Slip
    '/sales/return-slips': {
      isRestricted: !sales_config?.sub_modules?.return_slip?.is_active,
      moduleName: 'Return Slip',
      isForm: false,
    },
    '/sales/return-slips/view/:returnSlipId': {
      isRestricted: !sales_config?.sub_modules?.return_slip?.is_active,
      moduleName: 'Return Slip',
      isForm: false,
    },
    '/sales/return-slips/create': {
      isRestricted: !sales_config?.sub_modules?.return_slip?.is_active,
      moduleName: 'Return Slip',
      isForm: true,
    },
    '/sales/return-slips/update/:returnSlipId': {
      isRestricted: !sales_config?.sub_modules?.return_slip?.is_active,
      moduleName: 'Return Slip',
      isForm: true,
    },
    // Payment Incoming
    '/sales/incoming-payments': {
      isRestricted: !sales_config?.sub_modules?.payment_incoming?.is_active,
      moduleName: 'Incoming Payments',
      isForm: false,
    },
    '/sales/create-incoming-payments': {
      isRestricted: !sales_config?.sub_modules?.payment_incoming?.is_active,
      moduleName: 'Incoming Payments',
      isForm: true,
    },
    // E-Waybill
    '/sales/ewaybill': {
      isRestricted: !sales_config?.addons?.e_waybill?.is_active,
      moduleName: 'e-Way Bills',
      isForm: false,
    },

    // gate pass
    '/logistic/gate-document': {
      isRestricted: !logistics_config?.sub_modules?.gate_document?.is_active,
      moduleName: 'Gate Document',
      isForm: false,
    },
    '/logistic/gate-document/create': {
      isRestricted: !logistics_config?.sub_modules?.gate_document?.is_active,
      moduleName: 'Gate Document',
      isForm: false,
    },
    '/logistic/gate-document/view': {
      isRestricted: !logistics_config?.sub_modules?.gate_document?.is_active,
      moduleName: 'Gate Document',
      isForm: false,
    },

    // --> Settings
    // Approval Setup
    '/settings/workflow': {
      isRestricted: !global_config?.sub_modules?.approval_workflow?.is_active,
      moduleName: 'Approval Workflows',
      isForm: false,
    },
    '/analytics/forecasting': {
      isRestricted: !global_config?.sub_modules?.demand_forecasting?.is_active,
      moduleName: 'Demand Forecasting',
      isForm: false,
    },
    '/settings/warehouse-setup': {
      isRestricted: !global_config?.sub_modules?.warehouse_setup?.is_active,
      moduleName: 'Warehouse Setup',
      isForm: false,
    },
  };
};
