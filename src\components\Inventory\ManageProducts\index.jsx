import React, { Component, Fragment } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import { H3_TITLE } from '@Apis/constants';
import { Helmet } from 'react-helmet';
import { Tabs } from 'antd';
import PropTypes from 'prop-types';
import Helpers from '@Apis/helpers';
import BatchReservations from '@Components/Inventory/ManageProducts/BatchReservations';
import CFV2Actions from '@Actions/configurations/cfV2Actions';
import ProductsList from './ProductsList';
import InventoryLookup from './InventoryLookup';
import PriceList from './PriceList';
import DailyRunRateList from './DailyRunRate/DailyRunRateList';
import ProductGroups from './ProductGroups';
import './style.scss';

const { TabPane } = Tabs;

/**
 *
 */
class Inventory extends Component {
  /**
   *
   * @param props
   */
  constructor(props) {
    super(props);
    this.state = {
      showAddProductModal: false,
      currentTab: '/products-list',
    };
  }

  /**
   *
   * @param {*} props
   * @param {*} state
   * @return
   */

  componentDidMount() {
    const { getDocCFV2, user } = this.props;

    const payload = {
      orgId: user?.tenant_info?.org_id,
      entityName: 'PRODUCTS,BATCH',
    };
    getDocCFV2(payload);
  }

  static getDerivedStateFromProps(props, state) {
    const { location } = props;
    const key = new URLSearchParams(location.search).get('tab');
    return {
      ...state,
      currentTab: key || '/products-list',
    };
  }

  /**
   *
   * @return {JSX.Element}
   */
  render() {
    const { showAddProductModal, currentTab } = this.state;
    const { history, user } = this.props;

    return (
      <Fragment>
        <div>
          <div className="doc-list__tabs-wrapper">
            <Tabs
              activeKey={currentTab}
              onChange={(key) => {
                this.setState({ currentTab: key });
                history.push(`?tab=${key}`);
              }}
              mode="horizontal"
            >
              <TabPane tab="Products" key="/products-list" />
              <TabPane tab="Product Batches" key="/product-batches" />
              <TabPane tab="Reserved Stock" key="/batch-reservations" />
              {Helpers.getPermission(Helpers.permissionEntities.PRICE_LIST, Helpers.permissionTypes.READ, user) && <TabPane tab="Price Lists" key="/price-lists" />}
              <TabPane tab="Product Groups" key="/product-groups" />
              <TabPane tab="Stock Level Tracking" key="/daily-run-rates" />
            </Tabs>
            {currentTab === '/products-list' && (
              <ProductsList
                showAddProductModal={showAddProductModal}
                closeNewProductModal={() => this.setState({ showAddProductModal: false })}
              />
            )}
            {currentTab === '/product-groups' && (
              <ProductGroups />
            )}
            {currentTab === '/product-batches' && (
              <InventoryLookup />
            )}
            {currentTab === '/batch-reservations' && (
              <BatchReservations />
            )}
            {currentTab === '/price-lists' && (
              <PriceList />
            )}
            {currentTab === '/daily-run-rates' && (
              <DailyRunRateList />
            )}
          </div>
        </div>
      </Fragment>
    );
  }
}

const mapStateToProps = ({
  UserReducers,
}) => ({
  user: UserReducers.user,
});

const mapDispatchToProps = (dispatch) => ({
  getDocCFV2: (payload, callback) => dispatch(CFV2Actions.getDocCFV2(payload, callback)),
});

Inventory.propTypes = {
  location: PropTypes.func,
  history: PropTypes.object,
  user: PropTypes.any,
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(Inventory));
