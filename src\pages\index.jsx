import { lazy } from 'react';

const Home = lazy(() => import('./Home'));
const PendingPurchaseOrders = lazy(() => import('./Home/pendingPurchaseOrders'));
const PendingPaymentRequests = lazy(() => import('./Home/pendingPaymentRequests'));
const OutgoingPayments = lazy(() => import('./Home/outgoingPayments'));
const PendingPurchaseRequests = lazy(() => import('./Home/pendingPurchaseRequests'));
const PendingPurchaseIndent = lazy(() => import('./Home/pendingPurchaseIndents'));
const PendingExpenses = lazy(() => import('./Home/pendingExpenses'));
const PendingAccountPayableInvoices = lazy(() => import('./Home/pendingAccountPayableInvoices'));
const PendingSalesOrders = lazy(() => import('./Home/pendingSalesOrders'));
const PendingInvoices = lazy(() => import('./Home/pendingInvoices'));
const PendingGoodReceiving = lazy(() => import('./Home/pendingGoodReceiving'));
const PendingStockTransfer = lazy(() => import('./Home/pendingStockTransfer'));
const PendingBom = lazy(() => import('./Home/pendingBom'));
const PendingStockAdjustment = lazy(() => import('./Home/pendingStockAdjustment'));
const OpenStockTransfers = lazy(() => import('./Home/openStockTransfers'));
const Login = lazy(() => import('./login'));
const Indent = lazy(() => import('./Inventory/indent'));
const CreateIndent = lazy(() => import('./Inventory/createNewIndent'));
const UpdateIndent = lazy(() => import('./Inventory/updateIndent'));
const ManageVendors = lazy(() => import('./Vendors/manageVendors'));
const VendorProfile = lazy(() => import('./Vendors/viewVendorProfile'));
const PurchaseOrders = lazy(() => import('./Purchase/purchaseOrders'));
const CreatePurchaseOrder = lazy(() => import('./Purchase/createPurchaseOrder'));
const UpdatePurchaseOrder = lazy(() => import('./Purchase/updatePurchaseOrder'));
// Grn ->
const ViewGrnPage = lazy(() => import('./Purchase/Grn/viewGrnPage'));
const GoodsReceiving = lazy(() => import('./Purchase/goodsReceiving'));
const GRNForm = lazy(() => import('./Purchase/grnForm'));
const ViewStGrnPage = lazy(() => import('./Purchase/Grn/viewStGrnPage'));
const UpdateStGrnPage = lazy(() => import('./Purchase/Grn/updateStGrnPage'));
// Purchase  -> Debit Note
const ListDebitNotePage = lazy(() => import('./Purchase/DebitNote/listDebitNotesPage'));
const CreateDebitNotePage = lazy(() => import('./Purchase/DebitNote/createDebitNotesPage'));
const UpdateDebitNotePage = lazy(() => import('./Purchase/DebitNote/updateDebitNotesPage'));
const ViewDebitNotePage = lazy(() => import('./Purchase/DebitNote/viewDebitNotesPage'));

const ManageTeam = lazy(() => import('./Settings/mangeTeam'));
const Workflow = lazy(() => import('./Settings/workFlow'));
const NotFound = lazy(() => import('./notFound'));
const AccessControl = lazy(() => import('./Settings/accessControl'));
const MyProfile = lazy(() => import('./Settings/myProfile'));
const ViewPurchaseOrder = lazy(() => import('./Purchase/viewPurchaseOrder'));
const ViewVendorPayout = lazy(() => import('./Payments/viewVendorPayout'));
const PreviewCart = lazy(() => import('./Inventory/previewCart'));
const PreviewPO = lazy(() => import('./Inventory/previewPo'));

// Purchase --> Outgoing Payments
const CreateOutgoingPayment = lazy(() => import('./Purchase/createOutgoingPayment'));
const OutgoingPaymentsList = lazy(() => import('./Purchase/outgoingPaymentsList'));

// Sales --> Incoming Payments
const CreateIncomingPayment = lazy(() => import('./Sales/IncomingPayments/createIncomingPayment'));
const IncomingPaymentsList = lazy(() => import('./Sales/IncomingPayments/incomingPaymentsList'));

const PurchaseRequestsHome = lazy(() => import('./Purchase/PurchaseRequests/purchaseRequestsHome'));
const CreatePurchaseRequest = lazy(() => import('./Purchase/PurchaseRequests/createPurchaseRequest'));
const ViewPurchaseRequest = lazy(() => import('./Purchase/PurchaseRequests/viewPurchaseRequest'));
const UpdatePurchaseRequest = lazy(() => import('./Purchase/PurchaseRequests/updatePurchaseRequest'));
const PRSellerSelection = lazy(() => import('./Purchase/PurchaseRequests/prSellerSelection'));

const PurchaseIndentList = lazy(() => import('./Purchase/PurchaseIndent/purchaseIndentList'));
const CreatePurchaseIndent = lazy(() => import('./Purchase/PurchaseIndent/createPurchaseIndent'));
const UpdatePurchaseIndent = lazy(() => import('./Purchase/PurchaseIndent/updatePurchaseIndent'));
const ViewPurchaseIndent = lazy(() => import('./Purchase/PurchaseIndent/viewPurchaseIndent'));

const ExpenseHome = lazy(() => import('./Purchase/Expense/expenseHome'));
const ViewExpense = lazy(() => import('./Purchase/Expense/viewExpense'));

// Purchase -> Account Payable Invoice
const AccountPayableInvoiceList = lazy(() => import('./Purchase/AccountPayableInvoice/accountPayableInvoiceList'));
const AccountPayableInvoiceForm = lazy(() => import('./Purchase/AccountPayableInvoice/accountPayableInvoiceForm'));
const AccountPayableInvoiceView = lazy(() => import('./Purchase/AccountPayableInvoice/accountPayableInvoiceView'));

// Purchase -> RFQ
const RfqList = lazy(() => import('./Purchase/Rfq/rfqList'));
const RfqForm = lazy(() => import('./Purchase/Rfq/rfqForm'));
const ViewRfq = lazy(() => import('./Purchase/Rfq/viewRfq'));
const PublicRfq = lazy(() => import('./Purchase/Rfq/publicView'));

// Gate Document
const ListGateDocument = lazy(() => import('./Logistics/listGateDocument'));
const CreateGateDocument = lazy(() => import('./Logistics/createGateDocument'));
const ViewGateDocument = lazy(() => import('./Logistics/viewGateDocument'));

// Purchase -> Planned Purchase Order
const CreateBulkPo = lazy(() => import('./Purchase/createBulkPo'));
const UpdateBulkPo = lazy(() => import('./Purchase/updateBulkPo'));
const ViewBulkPO = lazy(() => import('./Purchase/viewBulkPO'));

// Sales Menu -> Customers
const Customer = lazy(() => import('./Sales/Customer/Customers'));
const CustomerProfile = lazy(() => import('./Sales/Customer/viewCustomerProfile'));

// Sales Menu -> Sales Orders
const ListSalesOrders = lazy(() => import('./Sales/SalesOrder/listSalesOrders'));
const CreateSalesOrder = lazy(() => import('./Sales/SalesOrder/createSalesOrder'));
const UpdateSalesOrder = lazy(() => import('./Sales/SalesOrder/updateSalesOrder'));
const ViewSalesOrder = lazy(() => import('./Sales/SalesOrder/viewSalesOrder'));

// Sales Menu -> Packing Slip
const CreatePackingSlip = lazy(() => import('./Sales/PackingSlip/createPackingSlip'));
const ListPackingSlip = lazy(() => import('./Sales/PackingSlip/listPackingSlip'));
const ViewPackingSlip = lazy(() => import('./Sales/PackingSlip/viewPackingSlip'));
const UpdatePackingSlip = lazy(() => import('./Sales/PackingSlip/updatePackingSlipForm'));

// Sales Menu -> Consumption
const CreateConsumption = lazy(() => import('./Sales/Consumption/createConsumption'));
const ListConsumption = lazy(() => import('./Sales/Consumption/listConsumption'));
const ViewConsumption = lazy(() => import('./Sales/Consumption/viewConsumption'));
const UpdateConsumption = lazy(() => import('./Sales/Consumption/updateConsumption'));

// Sales Menu -> Invoice
const ListInvoices = lazy(() => import('./Sales/Invoice/listInvoices'));
const CreateInvoice = lazy(() => import('./Sales/Invoice/createInvoice'));
const UpdateInvoice = lazy(() => import('./Sales/Invoice/updateInvoice'));
const ViewInvoice = lazy(() => import('./Sales/Invoice/viewInvoice'));

// Sales Menu -> E-invoicing & E-Waybills
const EinvoiceEwaybill = lazy(() => import('./Sales/EInvoiceEwaybill/einvoicingEwaybill'));
const EwaybillForm = lazy(() => import('./Sales/EInvoiceEwaybill/ewaybillForm'));

// Sales Menu -> Credit Note
const ListCreditNote = lazy(() => import('./Sales/CreditNote/listCreditNotes'));
const CreateCreditNote = lazy(() => import('./Sales/CreditNote/createCreditNote'));
const UpdateCreditNote = lazy(() => import('./Sales/CreditNote/updateCreditNote'));
const ViewCreditNote = lazy(() => import('./Sales/CreditNote/viewCreditNote'));

// Inventory -> Stock Transfer

const ListStockTransfer = lazy(() => import('./Inventory/StockTransfer/listStockTransfer'));
const CreateStockTransfer = lazy(() => import('./Inventory/StockTransfer/createStockTransfer'));
const UpdateStockTransfer = lazy(() => import('./Inventory/StockTransfer/updateStockTransfer'));
const ViewStockTransfer = lazy(() => import('./Inventory/StockTransfer/viewStockTransfer'));

// Production -> Manufacturing Orders

const ListManufacturingOrders = lazy(() => import('./Production/ManufacturingOrders/listManufacturingOrders'));
const CreateManufacturingOrder = lazy(() => import('./Production/ManufacturingOrders/createManufacturingOrderPage'));
const UpdateManufacturingOrder = lazy(() => import('./Production/ManufacturingOrders/updateManufacturingOrderPage'));
const ViewManufacturingOrder = lazy(() => import('./Production/ManufacturingOrders/viewManufacturingOrder'));
const MrpListV2 = lazy(() => import('./Production/MrpV2/mrpListV2'));
const ViewMrpV2 = lazy(() => import('./Production/MrpV2/viewMrpV2'));
const Configurations = lazy(() => import('./Production/Configurations'));
const BOM = lazy(() => import('./Production/Bom'));
const CreateBOM = lazy(() => import('./Production/Bom/createBom'));
const ViewBOM = lazy(() => import('./Production/Bom/viewBom'));
const JobWorks = lazy(() => import('./Production/JobWorks/listJobWorks'));

const ManualProductionEntryList = lazy(() => import('./Production/ManualProductionEntry/ManualProductionEntryList'));
const ManualProductionEntryForm = lazy(() => import('./Production/ManualProductionEntry/ManualProductionEntryForm'));
const ManualProductionEntryView = lazy(() => import('./Production/ManualProductionEntry/ManualProductionEntryView'));

// Quality --> Quality Control Points
const QCPointList = lazy(() => import('./Quality/QualityControlPoints/qcPointList'));
const ListQualityChecks = lazy(() => import('./Quality/QualityChecks/ListQualityChecks'));

// Admin Pages
const BusinessUnitsAdmin = lazy(() => import('./Admin/businessUnitsAdmin'));
const MemberAdmin = lazy(() => import('./Admin/userAdmin'));
const VendorsAdmin = lazy(() => import('./Admin/vendorsAdmin'));
const CustomersAdmin = lazy(() => import('./Admin/customersAdmin'));
const InventoryAdmin = lazy(() => import('./Admin/inventoryAdmin'));
const AdminConfiguration = lazy(() => import('./Admin/adminConfiguration'));

// DC
const ListDeliveryChallan = lazy(() => import('./Inventory/dc/listDeliveryChanllanPage'));
const ViewDeliveryChallan = lazy(() => import('./Inventory/dc/viewDeliveryChanllanPage'));
const CreateDeliveryChallan = lazy(() => import('./Inventory/dc/createDeliveryChallan'));

// Products Pages
const ManageProducts = lazy(() => import('./Inventory/Products/manageProducts'));
const ViewProduct = lazy(() => import('./Inventory/Products/viewProduct'));

// Integrations
const IntegrationPage = lazy(() => import('./Admin/integrations/integrationPage'));

// zoho
const ZohoHome = lazy(() => import('./Admin/integrations/zoho/zohoHomo'));
const ZohoSetup = lazy(() => import('./Admin/integrations/zoho/zohoSetup'));

// tally
const TallyHome = lazy(() => import('./Admin/integrations/tally/tallyHome'));
const TallySetup = lazy(() => import('./Admin/integrations/tally/tallySetup'));
const TallyIntegration = lazy(() => import('./Integrations/TallyIntegration'));

// busy
const BusyHome = lazy(() => import('./Admin/integrations/busy/busyHome'));
const BusySetup = lazy(() => import('./Admin/integrations/busy/busySetup'));

// unicommerce
const UnicommerceHome = lazy(() => import('./Admin/integrations/unicommerce/unicommerceHomo'));
const UnicommerceSetup = lazy(() => import('./Admin/integrations/unicommerce/unicommerceSetup'));

// shopify
const ShopifyHome = lazy(() => import('./Admin/integrations/shopify/shopifyHome'));
const ShopifySetup = lazy(() => import('./Admin/integrations/shopify/shopifySetup'));
const ShopifyConnect = lazy(() => import('./Admin/integrations/shopify/shopifyConnect'));

// whatsapp
const WhatsappHome = lazy(() => import('./Admin/integrations/whatsapp/whatsappHome'));
const WhatsappSetup = lazy(() => import('./Admin/integrations/whatsapp/whatsappSetup'));
const WhatsappConnect = lazy(() => import('./Admin/integrations/whatsapp/whatsappConnect'));

// reports
const ViewReportsPage = lazy(() => import('./Reports/viewReportsPage'));
const ReportsList = lazy(() => import('./Reports/reportsList'));

// reports v2

const ReportsListV2 = lazy(() => import('./ReportsV2/reportsListV2'));

// Bulk Upload
const BulkUpload = lazy(() => import('./BulkUpload/bulkUpload'));

const ForecastsList = lazy(() => import('./Forecast/forecastList'));
const ForecastForm = lazy(() => import('./Forecast/forecastForm'));
// settings
const WarehouseSetup = lazy(() => import('./Settings/warehouseSetup'));

const HelpDesk = lazy(() => import('./Home/helpdesk'));

// Purchase Indent -> Multi Vendor PO
const POSellerSelectionForm = lazy(() => import('./Purchase/PurchaseOrder/POSellerSelectionForm/poSellerSelectionForm'));

export {
  Home,
  PendingPurchaseOrders,
  PendingPaymentRequests,
  OutgoingPayments,
  PendingPurchaseRequests,
  PendingPurchaseIndent,
  PendingExpenses,
  PendingSalesOrders,
  PendingInvoices,
  PendingGoodReceiving,
  PendingStockTransfer,
  PendingBom,
  PendingStockAdjustment,
  OpenStockTransfers,
  Login,
  NotFound,
  ManageProducts,
  Indent,
  CreateIndent,
  UpdateIndent,
  ManageVendors,
  VendorProfile,
  PurchaseOrders,
  CreatePurchaseOrder,
  UpdatePurchaseOrder,
  GoodsReceiving,
  // CreateGoodsReceiving,
  // UpdateGoodsReceiving,
  GRNForm,
  ViewGrnPage,
  ManageTeam,
  Workflow,
  AccessControl,
  MyProfile,
  // Purchase
  ViewPurchaseOrder,
  CreateBulkPo,
  ViewBulkPO,
  UpdateBulkPo,
  ViewVendorPayout,
  PreviewCart,
  PreviewPO,

  // Purchase -> Account Payable Invoice
  AccountPayableInvoiceList,
  AccountPayableInvoiceForm,
  AccountPayableInvoiceView,
  PendingAccountPayableInvoices,

  // CreatePayout,
  CreateOutgoingPayment,

  // Admin
  BusinessUnitsAdmin,
  MemberAdmin,
  VendorsAdmin,
  CustomersAdmin,
  InventoryAdmin,

  OutgoingPaymentsList,
  CreateIncomingPayment,
  IncomingPaymentsList,
  AdminConfiguration,

  Customer,
  CustomerProfile,
  ListCreditNote,
  CreateCreditNote,
  UpdateCreditNote,
  ListInvoices,
  CreateInvoice,
  UpdateInvoice,
  ListSalesOrders,
  CreateSalesOrder,
  UpdateSalesOrder,
  ViewSalesOrder,
  CreatePackingSlip,
  ListPackingSlip,
  ViewPackingSlip,
  UpdatePackingSlip,
  ViewInvoice,
  ViewCreditNote,
  ListDebitNotePage,
  CreateDebitNotePage,
  UpdateDebitNotePage,
  ViewDebitNotePage,
  PurchaseRequestsHome,
  CreatePurchaseRequest,
  ViewPurchaseRequest,
  UpdatePurchaseRequest,
  PRSellerSelection,
  PurchaseIndentList,
  CreatePurchaseIndent,
  UpdatePurchaseIndent,
  ViewPurchaseIndent,
  ExpenseHome,
  ViewExpense,
  RfqList,
  RfqForm,
  ViewRfq,
  PublicRfq,

  // Gate Document
  ListGateDocument,
  CreateGateDocument,
  ViewGateDocument,

  // Consumption
  CreateConsumption,
  ListConsumption,
  ViewConsumption,
  UpdateConsumption,

  ListStockTransfer,
  CreateStockTransfer,
  UpdateStockTransfer,
  ViewStockTransfer,
  ListDeliveryChallan,
  ViewDeliveryChallan,
  CreateDeliveryChallan,
  ViewStGrnPage,
  UpdateStGrnPage,
  ViewProduct,

  // production
  ListManufacturingOrders,
  CreateManufacturingOrder,
  UpdateManufacturingOrder,
  ViewManufacturingOrder,
  MrpListV2,
  ViewMrpV2,
  Configurations,
  BOM,
  CreateBOM,
  ViewBOM,
  JobWorks,
  ManualProductionEntryList,
  ManualProductionEntryForm,
  ManualProductionEntryView,

  // Quality
  QCPointList,
  ListQualityChecks,

  // Integrations
  IntegrationPage,

  // Zoho
  ZohoHome,
  ZohoSetup,

  // Tally
  TallyHome,
  TallySetup,
  TallyIntegration,

  // Busy
  BusyHome,
  BusySetup,

  // unicommerce
  UnicommerceHome,
  UnicommerceSetup,

  // shopify
  ShopifyHome,
  ShopifySetup,
  ShopifyConnect,

  // whatsapp
  WhatsappHome,
  WhatsappSetup,
  WhatsappConnect,

  // Reports
  ReportsList,

  // Reports V2
  ReportsListV2,

  ViewReportsPage,
  ForecastsList,
  ForecastForm,
  // Setting
  WarehouseSetup,
  // BulkUpload
  BulkUpload,
  // E-invoicing E-waybills
  EinvoiceEwaybill,
  EwaybillForm,
  HelpDesk,

  // Purchase Indent -> Multi Vendor PO
  POSellerSelectionForm,
};
