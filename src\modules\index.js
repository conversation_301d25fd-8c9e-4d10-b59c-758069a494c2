import {
  all,
} from 'redux-saga/effects';
import * as KeyBoardShortcutModule from './keyBoardShortcut';
import * as GlobalSearchModule from './globalSearch';
import * as PurchaseOrders from './purchase/purchaseOrder';
import * as CreateSoFromPo from './createSoFromPo';
import * as ByProductMO from './byProductMO';
import * as GetShopifyErrors from './shopifyErrors';
import * as FavoriteEntity from './favoriteEntity';
import * as GetAnnualMaintenanceContractWorkflows from './workFlowAMC';
import * as GetTallyConfigurations from './getTallyConfigurations';
import * as DocSequenceModule from './documentSequence';
import * as PurchaseIndent from './purchase/purchaseIndent';
import * as APInvoice from './purchase/accountPayableInvoice';
import * as GRN from './purchase/grn';
import * as DeliveryChallan from './inventory/deliveryChallan';
import * as Expense from './purchase/expense';
import * as DevRevAccessToken from './devRevUserAccessToken';
import * as ProductionModule from './Production';

const modules = [
  KeyBoardShortcutModule, GlobalSearchModule, CreateSoFromPo,
  ByProductMO, GetShopifyErrors, FavoriteEntity, GetAnnualMaintenanceContractWorkflows,
  GetTallyConfigurations, PurchaseOrders, DocSequenceModule,
  PurchaseIndent, APInvoice, GRN, DeliveryChallan, Expense, DevRevAccessToken, ProductionModule,
];

export const Reducers = () => {
  const _result = new Map();
  modules.forEach((_m) => {
    Object.keys(_m).forEach((i) => {
      _result.set(i, _m[i].reducer);
    });
  });
  return _result;
};

export function* Sagas() {
  const sagas = [];
  modules.forEach((_m) => {
    Object.values(_m).forEach((i) => {
      sagas.push(i.saga());
    });
  });
  yield all(sagas);
}
