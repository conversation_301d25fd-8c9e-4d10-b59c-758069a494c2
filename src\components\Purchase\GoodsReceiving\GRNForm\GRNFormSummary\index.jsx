/* eslint-disable unicorn/prefer-structured-clone */
import SelectTaxType from '@Components/Admin/Common/SelectTaxType';
import FreightTaxInput from '@Components/Common/FreightTaxInput';
import PRZSelect from '@Components/Common/UI/PRZSelect';
import { faCircleInfo } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import H3FormInput from '@Uilib/h3FormInput';
import H3Text from '@Uilib/h3Text';
import { Radio, Tooltip, Upload } from 'antd';
import { Option } from 'antd/es/mentions';
import React, { Fragment, memo } from 'react';
import { ACTIONS } from '../reducer';
import constants from '@Apis/constants';
import { PlusCircleFilled, PlusOutlined } from '@ant-design/icons';
import { getLineTotals, splitChargesData } from '../helpers';
import Helpers from '@Apis/helpers';
import FormHelpers from '@Helpers/FormHelpers';

const uploadButtonForUploadingAttachments = (
  <div>
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  </div>
);

function GRNFormSummary({
  user,
  localDispatch,
  grnTypeValue,
  data,
  createGRNLoading,
  updateGRNLoading,
  selectedTenantTallyIntegrationId,
  narration,
  fileList,
  isApInvoiceEnabled,
  isDataMaskingPolicyEnable,
  isHideCostPrice,
  isLineWiseDiscount,
  selectedCurrencyInfo,
  MONEY,
  chargeData,
  charge1Value,
  taxTypeInfo,
  taxTypeName,
  freightTax,
  freightTaxData,
  renderCharges,
  terms,
  discountPercentage,
  formSubmitted,
  discountType,
  getBillFromAddress,
  getBillToAddress,
  charge1Name,
  freightTaxInfo,
  isReceivedQty,
  updateLandedCost,
  isVendorOverseas,
  freightTaxId,
  openFreightTax,
  freightSacCode,
  selectedSeller,
  selectedPoForGrn,
  taxTypeId,
  addNewChargesRow,
}) {

  return (
    <div className="form__data-wrapper">
      <div className="ant-row">
        <div className="ant-col-md-12">
          <div className="form__data-tc">
            <label className="orgFormLabel">
              Additional Remarks
            </label>
            <textarea
              className="orgFormInput"
              rows="4"
              onChange={(event) => {
                localDispatch({
                  type: ACTIONS.UPDATE_FIELDS,
                  payload: { terms: event.target.value },
                });
              }}
              disabled={createGRNLoading || updateGRNLoading}
              value={terms}
              style={{
                height: '100px',
              }}
            />
          </div>
          {!!selectedTenantTallyIntegrationId && <div className="form__data-tc">
            <label className="orgFormLabel">
              Narration
            </label>
            <textarea
              className="orgFormInput"
              rows="4"
              onChange={(event) => {
                localDispatch({
                  type: ACTIONS.UPDATE_FIELDS,
                  payload: { narration: event.target.value },
                });
              }}
              disabled={createGRNLoading || updateGRNLoading}
              value={narration}
              style={{
                minHeight: '40px',
                height: '60px',
              }}
            />
          </div>}
          <div className="form__data-attachment">
            <label className="orgFormLabel">Attachment(s)</label>
            <Upload
              action={constants.UPLOAD_FILE}
              listType="picture-card"
              fileList={fileList || []}
              disabled={createGRNLoading || updateGRNLoading}
              multiple
              onChange={(fileListData) => {
                localDispatch({
                  type: ACTIONS.UPDATE_FIELDS,
                  payload: { fileList: fileListData?.fileList?.map((item) => ({
                    ...item,
                    url:
                      item?.response?.response?.location || item?.url,
                  })) },
                });
              }}
            >
              {fileList?.length >= 20 ? null : uploadButtonForUploadingAttachments}
            </Upload>
          </div>
        </div>
        <div className="ant-col-md-12">
          <div className={isApInvoiceEnabled ? 'display-none' : 'form-calculator__wrapper'}>
            <div className="form-calculator">
              <div className="form-calculator__field">
                <H3Text
                  text="Sub Total"
                  className="form-calculator__field-name"
                />
                <H3Text
                  text={MONEY((getLineTotals({
                    data,
                    chargeData,
                    charge1Value,
                    grnTypeValue,
                    taxTypeInfo,
                    taxTypeName,
                    freightTax,
                    freightTaxData,
                  }).totalAmount || 0), selectedCurrencyInfo?.currency_code)}
                  className="form-calculator__field-value"
                  hideText={isDataMaskingPolicyEnable && isHideCostPrice}
                  popOverMessage={'You don\'t have access to view sub total amount'}
                />
              </div>
              {!isLineWiseDiscount && (
                <div className="form-calculator__field">
                  <H3Text
                    text="Discount"
                    className="form-calculator__field-name"
                  />
                  <H3Text
                    text={MONEY((getLineTotals({
                      data,
                      chargeData,
                      charge1Value,
                      grnTypeValue,
                      taxTypeInfo,
                      taxTypeName,
                      freightTax,
                      freightTaxData,
                    }).totalDiscount || 0), selectedCurrencyInfo?.currency_code)}
                    className="form-calculator__field-value"
                  />
                </div>
              )}
              {isLineWiseDiscount && selectedCurrencyInfo && (
                <div className="form-calculator__field">
                  <H3Text
                    text="Discount"
                    className="form-calculator__field-name"
                  />
                  <div
                    className="form-calculator__field-value"
                    style={{ display: 'flex' }}
                  >
                    <div style={{ width: '112px' }}>
                      <H3FormInput
                        value={discountPercentage}
                        type="number"
                        containerClassName={`${formSubmitted
                          && Number(discountPercentage) <= 0
                          ? 'form-error__input'
                          : ''
                          }`}
                        labelClassName="orgFormLabel"
                        inputClassName="orgFormInput"
                        onChange={(e) => {
                          const copyData = JSON.parse(JSON.stringify(data));
                          copyData.map((item) => {
                            const totalValue = copyData?.reduce((acc, cur) => acc + (cur.quantity * cur.offer_price), 0);

                            const discountValue = discountType === 'Percent' ? Number.parseFloat(e.target.value || 0) : ((item.quantity * item.offer_price) / Number.parseFloat(totalValue)) * Number.parseFloat(e.target.value || 0);

                            const taxableValue = discountType === 'Percent'
                              ? (item?.quantity * item?.offer_price) * (1 - discountValue / 100)
                              : Math.max(item.quantity * item?.offer_price - discountValue, 0);

                            item.discount = discountValue;
                            item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, getBillFromAddress(), getBillToAddress())?.tax_info?.child_taxes;
                            return data;
                          });
                          localDispatch({
                            type: ACTIONS.UPDATE_FIELDS,
                            payload: {
                              discountPercentage: Number.parseFloat(e.target.value || 0),
                              data: copyData,
                            },
                          });
                        }}
                      />
                    </div>
                    <div className="form-calculator__discount-type">
                      <PRZSelect
                        value={discountType}
                        onChange={(value) => {
                          const copyData = JSON.parse(JSON.stringify(data));
                          copyData.map((item) => {
                            const totalValue = copyData?.reduce((acc, cur) => acc + (cur.quantity * cur.offer_price), 0);

                            const discountValue = value === 'Percent' ? Number(discountPercentage) : ((item.quantity * item.offer_price) / Number.parseFloat(totalValue)) * Number(discountPercentage);

                            const taxableValue = value === 'Percent'
                              ? (item?.quantity * item?.offer_price) * (1 - discountValue / 100)
                              : Math.max(item.quantity * item?.offer_price - discountValue, 0);

                            item.lineDiscountType = value;
                            item.discount = discountValue;
                            item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, getBillFromAddress(), getBillToAddress())?.tax_info?.child_taxes;
                            return item;
                          });
                          localDispatch({
                            type: ACTIONS.UPDATE_FIELDS,
                            payload: { discountType: value, data: copyData },
                          });
                        }}
                      // disabled
                      >
                        <Option key="Amount" value="Amount">
                          {`${selectedCurrencyInfo?.currency_symbol}`}
                        </Option>
                        <Option key="Percent" value="Percent">
                          %
                        </Option>
                      </PRZSelect>
                    </div>
                  </div>
                </div>
              )}

              <div className="form-calculator__field">
                <div
                  className="form-calculator__field-name"
                  style={{ display: 'flex', alignItems: 'center' }}
                >
                  <H3Text
                    text={charge1Name}
                    style={{ marginRight: '10px' }}
                  />
                  {freightTax && (
                    <div style={{
                      color: '#2d7df7',
                      fontWeight: '400',
                      fontSize: '12px',
                    }}
                    >
                      {`tax@${freightTax}%`}
                    </div>
                  )}
                </div>
                <div
                  className="form-calculator__field-value"
                  style={{ display: 'flex' }}
                >
                  <div style={{ width: '112px' }}>
                    <H3FormInput
                      value={charge1Value}
                      type="number"
                      containerClassName={`${formSubmitted && Number(charge1Value) < 0
                        ? 'form-error__input'
                        : ''
                        }`}
                      labelClassName="orgFormLabel"
                      inputClassName="orgFormInput"
                      onChange={(e) => {
                        localDispatch({
                          type: ACTIONS.UPDATE_FIELDS,
                          payload: { charge1Value: e.target.value, freightTaxData: {
                            ...freightTaxData,
                            child_taxes: Helpers.computeTaxation(e.target.value, freightTaxInfo, getBillFromAddress(), getBillToAddress())?.tax_info?.child_taxes
                          } },
                        });
                        updateLandedCost(data, chargeData, e.target.value);
                      }}
                      disabled={!isReceivedQty}
                    />
                  </div>
                  <div className={isVendorOverseas ? 'display-none' : ''}>
                    <FreightTaxInput
                      freightTaxId={freightTaxId}
                      openFreightTax={openFreightTax}
                      sacCode={freightSacCode}
                      setOpenFreightTax={(value) => localDispatch({
                        type: ACTIONS.UPDATE_FIELDS,
                        payload: { openFreightTax: value },
                      })}
                      setFreightTaxData={(value) => {
                        localDispatch({
                          type: ACTIONS.UPDATE_FIELDS,
                          payload: {
                            freightTaxId: !value ? 'Not Applicable' : value?.tax_id,
                            freightTax: !value ? null : value?.tax_value,
                            freightTaxInfo: value ?? null,
                            freightTaxData: !value ? {
                              child_taxes: [
                                {
                                  tax_amount: 0,
                                  tax_type_name: '',
                                },
                              ],
                              tax_amount: 0,
                              tax_type_name: '',
                            } : {
                              ...value,
                              child_taxes: Helpers.computeTaxation(charge1Value, value, getBillFromAddress(), getBillToAddress())?.tax_info?.child_taxes
                            },
                          },
                        });
                      }}
                      setSacCode={(value) => localDispatch({
                        type: ACTIONS.UPDATE_FIELDS,
                        payload: { freightSacCode: value },
                      })}
                    />
                  </div>
                </div>
              </div>

              {renderCharges(splitChargesData(chargeData)?.chargeWithTaxName)}

              <div className="form-calculator__field">
                <H3Text
                  text="Taxable Amount"
                  className="form-calculator__field-name"
                />
                <H3Text
                  text={MONEY((getLineTotals({
                    data,
                    chargeData,
                    charge1Value,
                    grnTypeValue,
                    taxTypeInfo,
                    taxTypeName,
                    freightTax,
                    freightTaxData,
                  }).totalBase || 0), selectedCurrencyInfo?.currency_code)}
                  className="form-calculator__field-value"
                  hideText={isDataMaskingPolicyEnable && isHideCostPrice}
                  popOverMessage={'You don\'t have access to view taxable amount'}
                />
              </div>

              {!isVendorOverseas && (grnTypeValue === 'Purchase Order' ? data?.length > 0 : true) && data?.[0]?.child_taxes?.[0]?.tax_type_name && Helpers.groupAndSumByTaxName(FormHelpers.childTaxesData([...data, freightTaxData, ...(chargeData?.flatMap((charge) => charge?.chargesTaxData) || [])]))?.map((tax, i) => (
                <Fragment key={i}>
                  <div className="form-calculator__field">
                    <H3Text
                      text={tax?.tax_type_name}
                      className="form-calculator__field-name"
                    />
                    <H3Text
                      text={MONEY((tax?.tax_amount || '0'), selectedCurrencyInfo?.currency_code)}
                      className="form-calculator__field-value"
                      hideText={isDataMaskingPolicyEnable && isHideCostPrice}
                      popOverMessage={`You don't have access to view ${tax?.tax_type_name?.toLowerCase()}`}
                    />
                  </div>
                </Fragment>
              ))}

              {user?.tenant_info?.global_config?.settings?.enable_tds_tcs && user?.tenant_info?.country_code === 'IN' && (selectedSeller ? selectedSeller?.seller_info?.seller_type !== 'OVERSEAS' : selectedPoForGrn?.seller_info?.seller_type !== 'OVERSEAS') && (
                <div className="form-calculator__field">
                  <div
                    className="form-calculator__field-name"
                    style={{
                      display: 'flow',
                    }}
                  >
                    <Radio.Group
                      disabled={createGRNLoading || updateGRNLoading}
                      onChange={(event) => {
                        localDispatch({
                          type: ACTIONS.UPDATE_FIELDS,
                          payload: { taxTypeName: event.target.value, taxTypeId: '', taxTypeInfo: null },
                        });
                      }}
                      value={taxTypeName}
                    >
                      <Radio value="TDS">TDS</Radio>
                      <Radio value="TCS">TCS</Radio>
                    </Radio.Group>
                    <SelectTaxType
                      containerClassName="orgInputContainer"
                      selectedTaxType={taxTypeId}
                      disabled={createGRNLoading || updateGRNLoading}
                      onChange={(value) => {
                        localDispatch({
                          type: ACTIONS.UPDATE_FIELDS,
                          payload: { taxTypeId: value?.tax_id, taxTypeInfo: value, taxTypeName: value?.tax_type_name, taxType: value?.tax_type },
                        });
                      }}
                      taxTypeName={taxTypeName}
                      customStyle={{
                        width: '220px',
                        backgroundColor: 'white',
                      }}
                    />
                  </div>

                  {taxTypeName === 'TCS' ? (
                    <H3Text
                      text={MONEY((getLineTotals({
                        data,
                        chargeData,
                        charge1Value,
                        grnTypeValue,
                        taxTypeInfo,
                        taxTypeName,
                        freightTax,
                        freightTaxData,
                      }).totalTcs || '0'), selectedCurrencyInfo?.currency_code)}
                      className="form-calculator__field-value"
                    />
                  ) : (
                    <H3Text
                      text={MONEY((getLineTotals({
                        data,
                        chargeData,
                        charge1Value,
                        grnTypeValue,
                        taxTypeInfo,
                        taxTypeName,
                        freightTax,
                        freightTaxData,
                      }).totalTds || '0'), selectedCurrencyInfo?.currency_code)}
                      className="form-calculator__field-value"
                    />
                  )}
                </div>
              )}
              {renderCharges(splitChargesData(chargeData)?.chargeWithoutTaxName)}
              <div
                className="new-charge-row-button"
                onClick={() => addNewChargesRow()}
              >
                <span className="new-charge-row-button__icon">
                  <PlusCircleFilled />
                </span>
                <div>Add Charges</div>
              </div>
              {user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.round_off_method !== 'NO_ROUND_OFF' && (
                <div className="form-calculator__field">
                  <H3Text
                    text="Round Off"
                    className="form-calculator__field-name"
                  />
                  <Tooltip
                    title={`Round Off method for GRN is set to ${user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.round_off_method?.replace(/_/g, ' ')?.toProperCase()}`}
                  >
                    <div style={{ cursor: 'pointer', }}>
                      <FontAwesomeIcon icon={faCircleInfo} size='lg' style={{ color: '#2D7DF7', }} />
                    </div>
                  </Tooltip>
                  <H3Text
                    text={`${Helpers.configuredRoundOff(getLineTotals({
                      data,
                      chargeData,
                      charge1Value,
                      grnTypeValue,
                      taxTypeInfo,
                      taxTypeName,
                      freightTax,
                      freightTaxData,
                    }).grnTotal, user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.round_off_method)?.roundOff < 0 ? '(-) ' : ''}${MONEY(
                      Math.abs(Helpers.configuredRoundOff(getLineTotals({
                        data,
                        chargeData,
                        charge1Value,
                        grnTypeValue,
                        taxTypeInfo,
                        taxTypeName,
                        freightTax,
                        freightTaxData,
                      }).grnTotal, user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.round_off_method)?.roundOff),
                      selectedCurrencyInfo?.currency_code
                    )}`}
                    className="form-calculator__field-value"
                    hideText={
                      isDataMaskingPolicyEnable && isHideCostPrice
                    }
                    popOverMessage={
                      'You don\'t have access to view sub total'
                    }
                  />
                </div>
              )}
              <div className="form-calculator__field form-calculator__field-total">
                <H3Text
                  text="Grand Total"
                  className="form-calculator__field-name"
                />
                <H3Text
                  text={MONEY(Helpers.configuredRoundOff(getLineTotals({
                    data,
                    chargeData,
                    charge1Value,
                    grnTypeValue,
                    taxTypeInfo,
                    taxTypeName,
                    freightTax,
                    freightTaxData,
                  }).grnTotal || 0, user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.round_off_method)?.value, selectedCurrencyInfo?.currency_code)}
                  className="form-calculator__field-value"
                  hideText={isDataMaskingPolicyEnable && isHideCostPrice}
                  popOverMessage={'You don\'t have access to view grand total amount'}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default memo(GRNFormSummary);