.view-product__wrapper {
    margin-top: 85px;
    font-family: <PERSON>pin<PERSON>;

    .view-product__loader {
        .view-product-header__loader {
            display: flex;

            .view-product-header__loader-left {
                width: 90px;
                margin-right: 12px;
            }

            .view-product-header__loader-right {
                .view-product-header__loader-right-top {
                    width: 400px;
                    height: 25px;
                }

                .view-product-header__loader-right-mid {
                    margin-top: 8px;
                    width: 400px;
                    display: flex;
                    justify-content: space-between;
                    align-items: baseline;

                    .view-product-header__loader-right-mid-item {
                        width: 150px;
                        height: 18px;
                    }
                }

                .view-product-header__loader-right-bottom {
                    margin-top: 8px;
                    width: 100px;
                    height: 22px;
                }
            }
        }

        .view-product-mid__loader {
            display: flex;
            margin-top: 20px;

            .view-product-mid__loader-box {
                margin-right: 9px;
                border: 1px solid rgba(68, 123, 239, 0.2);
                padding: 12px;
                width: 200px;
                height: 66px;
                display: flex;

                .view-product-mid__loader-box-left {
                    width: 40px;
                    height: 40px;
                    margin-right: 10px;
                }

                .view-product-mid__loader-box-right {
                    .view-product-mid__loader-box-right-item {
                        height: 15px;
                        width: 100px;
                        margin-bottom: 10px;
                    }
                }
            }
        }
    }

    .view-product__header {
        display: flex;

        @media (max-width: 767px) {
            flex-direction: column;
        }

        .view-product__header-left {
            @media (max-width: 767px) {
                width: 100%;

            }

            .product__image {
                width: 90px;
                margin-right: 12px;
                border: 1px solid rgba(68, 123, 239, 0.2);
                border-radius: 5px;
                padding: 5px;

                @media (max-width: 767px) {
                    width: 50px !important;
                    height: 50px !important;
                }
            }

            .product__image-none {
                display: none;
            }

            .product__default {
                width: 90px;
                margin-right: 12px;
                opacity: 0.1;

                @media (max-width: 767px) {
                    width: 50px !important;
                }
            }

            .mobile-action-buttons {
                display: none;

                @media (max-width: 767px) {
                    display: block;
                    font-size: 16px !important;
                }
            }
        }

        .view-product__header-right {
            width: 100%;
            margin-left: 20px;

            .view-product__header-right-heading {
                font-size: 18px;
                font-weight: 500;
                display: flex;
                align-items: flex-start;

                .inactive-product-flag {
                    margin-left: auto;
                    color: #f68189;
                    font-weight: 400;
                    font-size: 22px;
                    background-color: rgba(238, 64, 76, 0.1);
                    padding: 5px 15px;
                    border-radius: 4px;
                }

                @media (max-width: 767px) {
                    display: none;
                }

                .action-buttons {
                    display: block;

                    @media (max-width: 767px) {
                        display: none;
                    }

                    .action-button {
                        height: 28px !important;
                    }
                }
            }

            .view-product__header-right-sub-heading__wrapper {
                display: flex;
                align-items: center;
                font-size: 12px;
                font-weight: 500;
                margin-top: 10px;

                @media (max-width: 767px) {
                    margin-top: 10px;
                    font-size: 11px;
                }

                .view-product__header-right-sub-heading {
                    display: flex;
                    align-items: center;
                    margin-right: 33px;

                    img {
                        width: 15px;
                        margin-right: 7px;
                    }


                }

            }

            .view-product__header-right-sub-heading-tag {
                background-color: #2D7DF7;
                color: white;
                width: fit-content;
                border-radius: 4px;
                padding: 2px 6px;
                margin-top: 8px;
                font-size: 12px;
                font-weight: 600;
            }

            .view-product__header-right-sub-heading-tag {
                @media (max-width: 767px) {
                    font-size: 11px !important;
                }
            }

        }
    }

    .view-product__info {
        margin-top: 20px;
        display: flex;
        flex-wrap: wrap;
        row-gap: 10px;

        .view-product__box {
            margin-right: 9px;
            border: 1px solid #447bef33;
            border-radius: 3px;
            padding: 8px;
            font-size: 10px;
            font-weight: 500;
            width: fit-content;
            display: flex;
            min-width: 148px;
            align-items: center;

            @media (max-width: 767px) {
                min-width: 37%;
            }

            .view-product__box-left {

                img,
                .anticon {
                    display: block;
                    font-size: 24px;
                    color: #2D7DF7;
                    width: 35px;
                    margin-right: 5px;

                    @media (max-width: 767px) {
                        display: none;
                    }
                }

                .exchange__icon {
                    width: 32px;
                }
            }

            .view-product__box-right {
                .view-product__box-right-heading {
                    &:hover {
                        cursor: pointer;
                    }
                }

                .view-product__box-right-sub-heading {
                    color: #447BEF;
                    margin-top: 5px;
                    width: 90px;
                    line-height: 16px;
                }
            }

            .view-product__box-right-exchange {
                .view-product__box-right-exchange__block {
                    display: flex;

                    .view-product__box-right-exchange-heading {
                        color: #447BEF;
                        width: 30px;
                    }

                }


            }
        }

        .view-product__box2,
        .view-product__box3,
        .view-product__box4 {
            background-color: #79b2591a;
            margin-right: 9px;
            border: 1px solid #79b25957;
            border-radius: 3px;
            padding: 8px;
            font-size: 12px;
            font-weight: 500;
            width: fit-content;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            @media (max-width: 767px) {
                width: 19%;
            }

            .view-product__box-top {
                width: 54px;
                text-align: center;
                color: #79b259;

                .anticon {
                    font-size: 28px;
                }

                img {
                    width: 27px;
                    color: #79b259;
                }

                .exchange__icon {
                    width: 32px;
                }
            }

            .view-product__box-bottom {
                .view-product__box-right-sub-heading {
                    color: #79b259;

                    @media (max-width: 767px) {
                        font-size: 11px !important;
                    }
                }
            }
        }

        .view-product__box3 {
            background-color: rgba(45, 124, 247, 0.10);
            border: 1px solid #2D7DF7;

            .view-product__box-bottom {
                .view-product__box-right-sub-heading {
                    color: rgba(45, 125, 247, 0.6);
                }
            }
        }

        .view-product__box4 {
            background-color: rgba(45, 124, 247, 0.10);
            border: 1px solid rgba(68, 123, 239, 0.2);

            .view-product__box-bottom {
                .view-product__box-right-sub-heading {
                    color: rgb(85 126 214);
                }
            }
        }
    }

    .view-product__tables {
        margin-top: 10px;

        .ant-tabs {
            .ant-tabs-tab {
                font-size: 13px !important;
                padding-bottom: 5px !important;
            }
        }
    }
}

.other-uom-stock {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 11px;
    line-height: 24px;
    background-color: rgba(45, 125, 247, 0.1);
    margin-bottom: 2px;
    padding: 0px 5px;
    font-weight: 500;
}


.view-product__tables {
    .view__activity-log {
        height: calc(100dvh - 350px);
        overflow-y: scroll;
        padding-top: 10px !important;

        .activity-log__item-message-subtext {
            font-size: 11px;
            opacity: 0.8 !important;
        }

        .activity-log__item-message {
            font-size: 12px !important;
            margin-top: -4px !important;
        }
    }
}

.view-product__secondary-uom-heading {
    font-size: 12px;
    font-weight: 500;
    margin-top: 12px;
}