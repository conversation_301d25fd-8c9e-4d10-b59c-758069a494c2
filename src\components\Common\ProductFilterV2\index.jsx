/* eslint-disable no-nested-ternary */
import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';

// Ant Design Components
import { Select, Drawer, Divider, Tooltip } from 'antd';
import { SwapLeftOutlined, SwapRightOutlined } from '@ant-design/icons';

// FontAwesome
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBinoculars } from '@fortawesome/free-solid-svg-icons';

// UI Components
import H3Image from '@Uilib/h3Image';
import H3Text from '@Uilib/h3Text';
import PRZButton from '../UI/PRZButton';
import PRZModal from '../UI/PRZModal';
import PRZSelect from '../UI/PRZSelect';

// App Components & Actions
import ProductActions from '@Actions/productActions';
import ProductForm from '@Components/Common/ProductForm';
import AdvanceProductSearch from '../AdvanceProductSearch';
import ProductCodesAndName from '../ProductCodesAndName';
import { useProductCodes } from '@Hooks/useProductCodes';

// Constants & Helpers
import Helpers from '@Apis/helpers';

// Images
import closeIcon from '@Images/icons/icon-close-blue.png';

// Styles
import './style.scss';

const { Option } = Select;

function ProductFilterV2({
  record, handleProductChangeValue, user, handleProductChange, shopifyProductsOnly, disabled, onClear, tenantDepartmentId,
  filterReservedQuantity, width, isRegularSelector, customClass, showError, hideAddNewProductOption, searchKeywordID,
  excludedProducts, excludeOutOfStock, isSalesProduct, isPurchaseProduct, showClear, selectedTenant, isMarketplaceSeller,
  productTypes, searchProductsLoading, searchProducts, searchResults, getOrgProductById, searchByProductCode,
  pendingFromVendor, reconciliationType, containerClassName, enableAdvanceSearch, handleMultiProductChange,
  loading, getProductByIdLoading, getOrgProductByIdLoading, showOnlyServiceProducts,
}) {
  const [searchKeyword, setSearchKeyword] = useState('');
  const [showAddProductModal, setShowAddProductModal] = useState(false);
  const [showAdvanceProductSearchDrawer, setShowAdvanceProductSearchDrawer] = useState(false);
  const [showPRZModal, setShowPRZModal] = useState(false);
  const [selectedDocumentId, setSelectedDocumentId] = useState(null);

  const tenantId = useMemo(() => selectedTenant || user?.tenant_info?.tenant_id, [selectedTenant, user]);

  const fireInitialSearch = useCallback((limit = 30) => {
    const keyword = (searchByProductCode && record?.alias_name)
      ? (searchKeyword || record?.alias_name)
      : (!record?.product_sku_id ? (searchKeyword || searchKeywordID) : '');

    const params = {
      keyword,
      tenantId,
      stockStatus: null,
      page: 1,
      limit,
      tenantDepartmentId: tenantDepartmentId || '',
      excludeOutOfStock,
      productType: productTypes?.join(','),
      category: null,
      isSalesProduct,
      isPurchaseProduct,
      search: true,
      filterReservedQuantity,
      barcode: null,
      isShopifyProduct: shopifyProductsOnly,
    };

    searchProducts(params);
  }, [
    searchByProductCode,
    record?.alias_name,
    record?.product_sku_id,
    searchKeyword,
    searchKeywordID,
    tenantId,
    tenantDepartmentId,
    excludeOutOfStock,
    productTypes,
    isSalesProduct,
    isPurchaseProduct,
    filterReservedQuantity,
    shopifyProductsOnly,
    searchProducts,
  ]);

  // ✅ Debounced search on keyword/type change
  useEffect(() => {
    const keyword = (searchByProductCode && record?.alias_name)
      ? (searchKeyword || record?.alias_name)
      : (!record?.product_sku_id ? (searchKeyword || searchKeywordID) : null);

    if (keyword) {
      const handler = setTimeout(() => fireInitialSearch(30), 300);
      return () => clearTimeout(handler);
    }
  }, [
    searchKeyword,
    searchByProductCode,
    record?.product_sku_id,
    record?.alias_name,
    searchKeywordID,
    fireInitialSearch,
  ]);

  const filteredProducts = useMemo(() => (
    searchResults?.result?.products?.filter((item) => (isMarketplaceSeller ? item?.is_marketplace_product : true)) || []
  ), [searchResults, isMarketplaceSeller]);

  const { enableInternalRefCode, enableInternalSKUCode } = useProductCodes();

  return (
    <div className={containerClassName || 'product-filter__wrapper'}>
      {/* Add Product Drawer */}
      <Drawer open={showAddProductModal} onClose={() => setShowAddProductModal(false)} width="720px" destroyOnClose>
        <div className="custom-drawer__header-wrapper">
          <div className="custom-drawer__header" style={{ width: '680px' }}>
            <H3Text text="Add New Product" className="custom-drawer__title" />
            <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => setShowAddProductModal(false)} />
          </div>
        </div>
        <ProductForm
          selectedTenant={selectedTenant}
          callback={(createdProduct) => {
            searchProducts({ keyword: createdProduct?.internal_sku_code, tenantId }, (data) => {
              handleProductChange(data, record?.key, createdProduct, false, () => {}, showOnlyServiceProducts);
              setShowAddProductModal(false);
            });
          }}
        />
      </Drawer>

      {/* Advance Product Search Drawer */}
      <Drawer open={showAdvanceProductSearchDrawer} onClose={() => setShowAdvanceProductSearchDrawer(false)} width="900px" destroyOnClose mask={false}>
        <div className="custom-drawer__header-wrapper">
          <div className="custom-drawer__header" style={{ width: '850px' }}>
            <H3Text text="Advance Product Search" className="custom-drawer__title" />
            <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => setShowAdvanceProductSearchDrawer(false)} />
          </div>
        </div>
        <AdvanceProductSearch
          selectedTenant={tenantId}
          tenantDepartmentId={tenantDepartmentId || ''}
          excludeOutOfStock={excludeOutOfStock}
          productTypes={productTypes}
          isSalesProduct={isSalesProduct}
          isPurchaseProduct={isPurchaseProduct}
          filterReservedQuantity={filterReservedQuantity}
          isShopifyProduct={shopifyProductsOnly}
          onSelectedProduct={(selected, cb) => {
            const productSkuIds = selected?.map(i => i?.product_info?.product_sku_id)?.join(',');
            getOrgProductById(user?.tenant_info?.org_id, tenantId, '', (data) => {
              handleMultiProductChange(selected, record?.key, data, cb, showOnlyServiceProducts);
            }, pendingFromVendor, productSkuIds);
          }}
          excludedProducts={excludedProducts}
          loading={loading || getProductByIdLoading || getOrgProductByIdLoading}
          disableProductTypeFilter={showOnlyServiceProducts}
        />
      </Drawer>

      {/* Product Selector */}
      {((record?.product_sku_id || record?.product_sku_info?.product_sku_id) && !isRegularSelector) ? (
        <div style={{ display: 'flex', justifyContent: 'space-between' }} onClick={() => {
          setShowPRZModal(true);
          setSelectedDocumentId(record?.product_sku_id || record?.product_sku_info?.product_sku_id);
        }}>
          <ProductCodesAndName
            skuCode={record?.tenant_product_info?.internal_sku_code || record?.internal_sku_code || record?.product_info?.internal_sku_code}
            refCode={record?.ref_product_code || record?.product_info?.ref_product_code}
            name={record?.product_sku_name || record?.product_info?.product_sku_name}
            showSku={enableInternalSKUCode}
            showRefCode={enableInternalRefCode}
            productNameClassWrapper="product-filter__text"
          />
          {reconciliationType && (
            <div className="ia-type-tag" style={{ backgroundColor: reconciliationType === 'OUTWARD' ? '#f15353' : '#2fbc6e' }}>
              {reconciliationType === 'OUTWARD' ? <SwapRightOutlined /> : <SwapLeftOutlined />}
              <H3Text text={reconciliationType.toProperCase()} />
            </div>
          )}
        </div>
      ) : (
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', gap: '5px' }}>
          <div style={{ width: '100%' }}>
            <PRZSelect
              className={customClass}
              value={record?.product_sku_id ? parseInt(record?.product_sku_id) : null}
              filterOption={false}
              showSearch
              onSearch={(keyword) => {
                handleProductChangeValue(keyword, record?.key);
                setSearchKeyword(keyword);
              }}
              placeholder="search product.."
              searchValue={searchKeyword}
              placement="bottomLeft"
              onFocus={() => fireInitialSearch()}
              onChange={(value) => {
                getOrgProductById(user?.tenant_info?.org_id, tenantId, value, (data) => {
                  handleProductChange(filteredProducts.find(i => i?.product_sku_id === value), record?.key, data, false, () => {}, showOnlyServiceProducts);
                }, pendingFromVendor);
              }}
              status={showError ? 'warning' : ''}
              style={{ borderRadius: '2px', height: '28px', width: width || '100%', fontSize: '13px' }}
              disabled={disabled}
              loading={searchProductsLoading}
              dropdownRender={
                Helpers.getPermission(Helpers.permissionEntities.PRODUCT, Helpers.permissionTypes.CREATE, user) && !hideAddNewProductOption ? (menu) => (
                  <>
                    {menu}
                    <Divider style={{ margin: 0 }} />
                    <div className="add__product" onClick={() => setShowAddProductModal(true)}>+ Add Product</div>
                  </>
                ) : undefined
              }
              showError={showError}
              errorName="a product"
              errorClassName="input-error"
            >
              {filteredProducts.map((item) => (
                <Option
                  key={item?.product_info?.product_sku_id}
                  value={item?.product_info?.product_sku_id}
                  disabled={excludedProducts?.includes(JSON.stringify(item?.tenant_product_id)) || searchProductsLoading}
                >
                  <Tooltip
                    title={<><div>#{item?.product_info?.internal_sku_code}</div><div>{item?.product_info?.ref_product_code}</div></>}
                    placement="right"
                  >
                    <div className="product-filter__option">
                      <H3Text
                        text={`${enableInternalSKUCode ? `#${item?.product_info?.internal_sku_code}` : ''} ${item?.product_info?.product_sku_name}`}
                        className="product-filter__option-text"
                      />
                      <div style={{ display: 'flex', marginTop: '5px' }}>
                        {['BUNDLE', 'NON_STORABLE', 'SERVICE'].includes(item?.product_info?.product_type) && <H3Text text={item?.product_info?.product_type?.replaceAll('_', ' ')?.toProperCase()} className="status-tag" />}
                        {!['NON_STORABLE', 'SERVICE'].includes(item?.product_info?.product_type) && (
                          <div className="status-tag" style={{ backgroundColor: Number(item?.available_qty) <= 0 ? 'red' : '' }}>
                            {`${(item?.available_qty - Number(item?.reserved_quantity || '0')) || '0'} ${item?.product_info?.uom_info?.uqc?.toProperCase()}${item?.reserved_quantity ? ` (+${Number(item?.reserved_quantity)} Reserved)` : ''}`}
                          </div>
                        )}
                      </div>
                    </div>
                  </Tooltip>
                </Option>
              ))}
            </PRZSelect>
          </div>
          {enableAdvanceSearch && (
            <PRZButton
              disabled={disabled || showAdvanceProductSearchDrawer}
              type="dashed"
              buttonStyle={{ width: '32px', height: '32px', minWidth: '32px', padding: '0px', borderRadius: '50%' }}
              onClick={() => setShowAdvanceProductSearchDrawer(true)}
            >
              <FontAwesomeIcon icon={faBinoculars} />
            </PRZButton>
          )}
        </div>
      )}

      {(showClear && record?.product_sku_id) && (
        <H3Text onClick={() => onClear(record.key)} text={<div>remove</div>} className="table-subscript-action" />
      )}
      {(record?.product_type === 'BUNDLE' && showClear) && (
        <H3Text text="Bundle" className="table-subscript__product-type" />
      )}

      <PRZModal
        isOpen={showPRZModal}
        onClose={() => {
          setShowPRZModal(false);
          setSelectedDocumentId(null);
        }}
        entityType="product"
        documentId={selectedDocumentId}
      />
    </div>
  );
}

const mapStateToProps = ({ ProductReducers, UserReducers }) => ({
  searchResults: ProductReducers.searchResults,
  searchProductsLoading: ProductReducers.searchProductsLoading,
  user: UserReducers.user,
  getProductByIdLoading: ProductReducers.getProductByIdLoading,
  getOrgProductByIdLoading: ProductReducers.getOrgProductByIdLoading,
});

const mapDispatchToProps = (dispatch) => ({
  searchProducts: (params, cb) => dispatch(ProductActions.searchProducts(params, cb)),
  getOrgProductById: (orgId, tenantId, skuId, cb, vendor, ids) => dispatch(ProductActions.getOrgProductById(orgId, tenantId, skuId, cb, vendor, ids)),
});

export default React.memo(connect(mapStateToProps, mapDispatchToProps)(withRouter(ProductFilterV2)));