// React & Redux
import React, { Fragment, useEffect, useMemo, useState } from 'react';
import { <PERSON>, withRouter } from 'react-router-dom';
import { connect } from 'react-redux';

// Ant Design
import { Drawer, Radio, Menu, Dropdown } from 'antd';
import { MenuOutlined, CaretDownOutlined, LoadingOutlined, CloseOutlined } from '@ant-design/icons';

// FontAwesome Icons
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faIndustry, faTags, faBagShopping, faCartShopping, faMoneyBills, faVideo, faCircleXmark, faWarehouse, faHome, faAnglesLeft, faMessage } from '@fortawesome/free-solid-svg-icons';

// Actions
import TrackerActions from '@Actions/trackerActions';
import UserActions from '@Actions/userActions';
import CartActions from '@Actions/cartActions';
import AddToCartActions from '../../../actions/addToCart/addToCartActions';

// UI Libraries
import H3Image from '@Uilib/h3Image';
import H3Text from '@Uilib/h3Text';

// Helpers & Constants
import Helpers from '@Apis/helpers';
import { Pages, AdminPages } from '@Apis/constants';
import documentUINamesConstant from '../../../apis/documentUINamesConstant';
import { iconLibrary, userMenuConstant } from '../../../apis/userMenu';

// Components
import KeyboardShortcutHandler from '../../KeyboardShortcutHandler';
import GlobalSearch from '../../GlobalSearch';

// Images
import userIcon from '@Images/usericon.png';
import Crown from '@Images/crown2.png';
import searchIcon from '@Images/icons/icon-search.png';
import rightArrow from '@Images/icons/icon-arrow-right-blue.png';
import logo from '@Images/logo-without-tagline.png';
import WhatsApp from '../../../media/images/whatsapp.png';

// Styles
import './style.scss';

const { SubMenu } = Menu;

/**
 *
 */
function Header({ pageAction, history, user, getOrg, switchBusinessUnit, org, match, logoutUser, cartDataV2, getCartV2 }) {
  const [mobileSlider, setMobileSlider] = useState(false);
  const [isUserReady, setIsUserReady] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedTenant, setSelectedTenant] = useState('');
  const [currentPage, setCurrentPage] = useState('');
  const [openKeys, setOpenKeys] = useState([]);
  const [config, setConfig] = useState(null);
  const [showHelpdesk, setShowHelpdesk] = useState(false);
  const [showSwitchBuDrawer, setShowSwitchBuDrawer] = useState(false);
  const [helpdeskLoaded, setHelpdeskLoaded] = useState(false);
  const [differenceInDays, setDifferenceInDays] = useState(0);
  const [orgIsActive, setOrgIsActive] = useState(false);
  const [call, setCall] = useState(false);

  const documentUINames = useMemo(() => documentUINamesConstant(user), [user]);

  useEffect(() => {
    pageAction(findPageName());
    if (!org?.organisation?.[0]?.org_id) {
      getOrg(user?.tenant_info?.org_id);
    }
    if (history.location.pathname) {
      setCurrentPage(history.location.pathname);
    }
    if (history.location.pathname.includes('/inventory')) {
      setOpenKeys(['/inventory']);
    }
    if (history.location.pathname.includes('/production')) {
      setOpenKeys(['/production']);
    }
    if (history.location.pathname.includes('/quality')) {
      setOpenKeys(['/quality']);
    }
    if (history.location.pathname.includes('/settings')) {
      setOpenKeys(['/settings']);
    }
    if (
      history.location.pathname.includes('/purchase') ||
      history.location.pathname.includes('/approval')
    ) {
      setOpenKeys(['/purchase']);
    }
    if (history.location.pathname.includes('/payments')) {
      setOpenKeys(['/payments']);
    }
    if (history.location.pathname.includes('/sales')) {
      setOpenKeys(['/sales']);
    }
    if (history.location.pathname.includes('/analytics')) {
      setOpenKeys(['/analytics']);
    }

    const subscriptionEndDate =
      user?.tenant_info?.global_config?.settings?.subscription_end_date;
    const providedEndDate = new Date(subscriptionEndDate);
    const currentDate = new Date();
    const differenceInMilliseconds = providedEndDate - currentDate;
    const diffInDays = differenceInMilliseconds / (1000 * 60 * 60 * 24);
    setDifferenceInDays(Math.floor(diffInDays));
    setOrgIsActive(user?.org_is_active);
  }, []);

  const getPageName = () => {
    const pathMapping = {
      '/purchase/purchase-orders/create': 'New Purchase Order',
      '/purchase/goods-receiving/create': 'New Goods Receiving',
      '/purchase/bulk-purchase-orders/create': 'New Planned Purchase Order',
      '/purchase/purchase-request': 'All Material Requests',
      '/purchase/purchase-request/create': 'New Material Requests',
      '/purchase/purchase-indent': 'All Purchase Indent',
      '/purchase/purchase-indent/create': 'New Purchase Indent',
      '/purchase/vendors/manage': 'All Vendors',
      '/inventory/manage': 'Manage Inventory',
      '/purchase/purchase-orders': 'All Purchase Orders',
      '/settings/team': 'Manage Team',
      '/settings/business-units': 'Mange Business Unit',
      '/settings/workflow': 'Manage Approval Workflows',
      '/purchase/goods-receiving': 'All Goods Receiving Notes',

      // Purchase -> Accounts Payable Invoice
      '/purchase/account-payable-invoice': 'Accounts Payable Invoices',
      '/purchase/account-payable-invoice/create': 'New Accounts Payable Invoice',
      '/purchase/account-payable-invoice/update': 'Update Accounts Payable Invoice',
      '/purchase/account-payable-invoice/view': 'View Accounts Payable Invoice',

      // Purchase -> Debit Notes
      '/purchase/debit-notes': 'All Debit Notes',
      '/inventory/indent': 'Stock Adjustments',
      '/inventory/indent/create': 'New Stock Adjustments',
      '/inventory/delivery-challan': 'Delivery Challan',
      '/settings/access-control': 'Access Control',
      '/settings/business-profile': 'Business Profile',
      '/purchase/vendor-payouts': 'Outgoing Payments',
      '/logistic/gate-document': 'Gate Document',
      '/logistic/gate-document/create': 'New Gate Document',
      '/settings/warehouse-setup': 'Warehouse Setup',
      '/payments/payment-requests': 'Payment Request',
      '/payments/create': 'New Vendor Payment',
      '/sales/create-incoming-payments': 'New Incoming Payment',
      '/inventory/preview-cart': 'Preview Cart',
      '/inventory/preview-purchase-orders': 'Preview Purchase Orders',
      '/reports': 'Reports',
      // Sales Menu
      '/sales/customers': 'All Customers',
      '/sales/incoming-payments': 'Incoming Payments',

      // Sales Menu -> Invoice
      '/sales/invoices': 'All Invoices',
      '/sales/invoices/create': 'Create Invoice',

      // Sales Menu -> E-invoicing  & E-waybill
      '/sales/ewaybill': 'e-Way Bills',
      '/sales/ewaybill/create': 'Create E-waybills',

      // Sales Menu -> Estimate
      '/sales/estimate': `All ${documentUINames.estimateUIName}s`,
      '/sales/estimate/create': `Create ${documentUINames.estimateUIName}s`,

      // Sales Menu -> Order
      '/sales/sales-orders': `All ${documentUINames.salesOrderUIName}s`,
      '/sales/sales-orders/create': `Create ${documentUINames.salesOrderUIName}s`,

      // Sales Menu -> Packing Slip
      '/sales/packing-slips': 'All Packing Slips',
      '/sales/packing-slips/create': 'Create Packing Slips',

      // Sales Menu -> consumption
      '/sales/consumption': 'All Consumption Orders',
      '/sales/consumption/create': 'Create Consumption Order',

      // Sales Menu -> Credit Note
      '/sales/credit-notes': 'All Credit Notes',
      '/sales/credit-notes/create': 'Create Credit Notes',

      // Sales Menu -> Return Slip
      '/sales/return-slips': 'All Return Slips',
      '/sales/return-slip/create': 'Create Return Slips',

      // Inventory -> Stock Transfer
      '/inventory/stock-transfer': 'All Stock Transfers',
      '/inventory/stock-transfer/create': 'Create Stock Transfer',

      // Production -> Manufacturing Order
      '/production/bom': 'Bill of Material',
      '/production/bom/create': 'New Bill of Material',
      '/production/manufacturing-orders': 'All Manufacturing Orders',
      '/production/manufacturing-orders/create': 'New Manufacturing Orders',

      '/production/planning': 'Materials Requirement Planning',
      '/production/planning/v2': 'Materials Requirement Planning',
      '/production/planning/v2/create': 'New Materials Requirement Planning',
      '/production/configurations': 'Factory Setup',
      '/production/job-work': 'Job Card',
      '/production/job-works': 'Job Cards',

      '/production/manual-entry': 'All Manual Entries',
      '/production/manual-entry/create': 'Create Manual Production Entry',

      '/actions/pending-purchase-orders': 'Purchase Orders Pending on Your Approval',
      '/actions/pending-purchase-requests': 'Material Request Pending on Your Approval',
      '/actions/pending-purchase-indents': 'Purchase Indents Pending on Your Approval',
      '/actions/pending-payment-approvals': 'Payment Requests Pending on Your Approval',
      '/actions/pending-expenses': 'Expenses Pending on Your Approval',
      '/actions/approved-payments': 'Approved Payments Pending on Disbursal',
      '/actions/pending-estimates': `${documentUINames.estimateUIName} Pending in Your Approval`,
      '/actions/pending-sales-orders': `${documentUINames.salesOrderUIName} Pending in Your Approval`,
      '/actions/pending-invoices': 'Invoices Pending for Your Approval',
      '/actions/pending-good-receiving': 'Goods Receiving Pending for Your Approval',
      '/actions/pending-account-payable-invoices': 'Account Payable Invoices Pending for Your Approval',
      '/actions/pending-stock-adjustment': 'Stock Adjustments Pending for Your Approval',
      '/actions/pending-stock-transfer': 'Stock Transfers Pending for Your Approval',
      '/actions/open-stock-transfers': 'Open Stock Transfers pending for Goods Receiving',
      '/actions/pending-bom-approvals': 'Bill of Materials Pending in Your Approval',

      // Quality --> Quality Check Points
      '/quality/quality-control-points': 'Quality Control Rules',
      '/quality/quality-checks': 'Quality Checks',

      // Admin Heading
      '/admin/org': 'My Organisation',
      '/admin/business-units': 'All Business Units',
      '/admin/inventory': 'All Units Inventory',
      '/admin/vendors': 'All Units Vendors',
      '/admin/customers': 'All Units Customers',
      '/admin/configuration': 'Configuration',
      '/admin/integration': 'Integrations',
      // Expense
      '/purchase/expense': 'Expenses',
      // Bulk Upload
      '/analytics/bulk-uploads': 'Bulk Uploads',

      // Tally
      '/integration/tally': 'Tally Sync',
    };
    if (pathMapping[match.path]) return pathMapping[match.path];
    if (match.path.includes('/vendors/view/')) return 'View Vendor Profile';
    if (match.path.includes('/purchase/goods-receiving/update/'))
      return 'Update Goods Receiving';
    if (match.path.includes('/purchase/goods-receiving/view/'))
      return 'View Goods Receiving';
    if (match.path.includes('/purchase/account-payable-invoice/view/'))
      return 'View Account Payable Invoice';
    if (match.path.includes('/inventory/indent/update'))
      return 'Update Stock Adjustment';
    if (match.path.includes('/purchase/purchase-orders/update'))
      return 'Update Purchase Order';
    if (match.path.includes('/purchase/account-payable-invoice/update'))
      return 'Update Account Payable Invoice';

    // Puchase -> BulkPO
    if (match.path.includes('/purchase/bulk-purchase-orders/view'))
      return 'View Planned Purchase Order';
    if (match.path.includes('/purchase/bulk-purchase-orders/update'))
      return 'Update Planned Purchase Order';

    //  Sales Menu -> Estimate
    if (match.path.includes('/sales/estimate/update'))
      return `Update ${documentUINames.estimateUIName}`;
    if (match.path.includes('/sales/estimate/view'))
      return `View ${documentUINames.estimateUIName}`;

    //  Sales Menu -> Invoice
    if (match.path.includes('/sales/invoices/update')) return 'Update Invoice';
    if (match.path.includes('/sales/invoice/view')) return 'View Invoice';

    //  Sales Menu -> Consumption order
    if (match.path.includes('/sales/consumption/view'))
      return 'View Consumption Order';

    // Inventory -> Stock Transfer
    if (match.path.includes('/inventory/stock-transfer/update'))
      return 'Update Stock Transfer';
    if (match.path.includes('/inventory/stock-transfer/view'))
      return 'View Stock Transfer';

    // Inventory -> Delivery Challan
    if (match.path.includes('/inventory/delivery-challan/update'))
      return 'Update Delivery Challan';
    if (match.path.includes('/inventory/delivery-challan/view'))
      return 'View Delivery Challan';
    if (match.path.includes('/inventory/delivery-challan/create'))
      return 'Create Delivery Challan';
    // Inventory -> Product
    if (match.path.includes('/inventory/product/view'))
      return 'Product Information';

    if (match.path.includes('/sales/credit-notes/update')) return 'Update Credit Note';
    if (match.path.includes('/sales/credit-note/view')) return 'View Credit Note';
    if (match.path.includes('/sales/return-slip/view')) return 'View Return Slip';
    if (match.path.includes('/sales/return-slip/update')) return 'Update Return Slip';
    if (match.path.includes('/purchase/debit-notes/create')) return 'Create Debit Note';
    if (match.path.includes('/purchase/debit-notes/update')) return 'Update Debit Note';
    if (match.path.includes('/purchase/debit-note/view')) return 'View Debit Note';
    if (match.path.includes('/sales/sales-orders/update')) return `Update ${documentUINames.salesOrderUIName}`;
    if (match.path.includes('/sales/amc-sales-orders/update')) return `Update ${documentUINames.amcSalesOrderUIName}`;
    if (match.path.includes('/sales/sales-order/view')) return `View ${documentUINames.salesOrderUIName}`;
    if (match.path.includes('/sales/amc-sales-order/view')) return `View ${documentUINames.amcSalesOrderUIName}`;
    if (match.path.includes('/sales/packing-slip/update')) return 'Update Packing Slip';
    if (match.path.includes('/sales/packing-slip/view')) return 'View Packing Slip';
    if (match.path.includes('/approval/payout')) return 'Approve Payout';
    if (match.path.includes('/approval')) return 'View Purchase Order';
    if (match.path.includes('/logistic/gate-document/view/')) return 'View Gate Document';
    if (match.path.includes('/sales/customer/view/')) return 'View Customer Profile';
    if (match.path.includes('/purchase/purchase-request/view')) return 'View Material Request';
    if (match.path.includes('/purchase/purchase-request/update')) return 'Update Material Request';
    if (match.path.includes('/purchase/purchase-indent/view')) return 'View Purchase Indent';
    if (match.path.includes('/purchase/purchase-indent/update')) return 'Update Purchase Indent';
    if (match.path.includes('/purchase/purchase-request/multi-vendor')) return 'Create Purchase Orders for Material Request';

    // Production -> Manufacturing Order
    if (match.path.includes('/production/manufacturing-orders/view'))
      return 'View Manufacturing Order';
    if (match.path.includes('/production/bom/view'))
      return 'View Bill of Material';
    if (match.path.includes('/production/bom/update/'))
      return 'Update Bill of Material';
    if (match.path.includes('/production/manufacturing-orders/update'))
      return 'Update Manufacturing Order';
    if (match.path.includes('/production/manual-entry/view'))
      return 'View Production Entry';

    if (match.path.includes('/production/planning/view'))
      return 'View Materials Requirement Plan';
    if (match.path.includes('/production/planning/v2/create'))
      return 'New Materials Requirement Planning';
    if (match.path.includes('/production/planning/v2/view'))
      return 'View Materials Requirement Planning';

    // Admin -> Integrations;
    if (match.path.includes('/admin/integration')) return 'Integrations';

    // Reports -> View Report;
    if (match.path.includes('/reports/')) return 'View Report';

    // Expense ->  Expenses;
    if (match.path.includes('/purchase/expense/')) return 'Expenses';

    // RFQ
    if (match.path.includes('/purchase/rfq/view'))
      return 'View Request For Quotation';
    if (match.path.includes('/purchase/rfq')) return 'Request For Quotation';

    if (match.path.includes('/analytics/forecasting/view'))
      return 'View Forecast Details';
    if (match.path.includes('/analytics/forecasting'))
      return 'Demand Forecasting';

    // bulk Upload
    if (match.path.includes('/analytics/bulkUpload')) return 'Bulk Uploads';

    // Purchase Indent -> Multi Vendor PO
    if (match.path.includes('/purchase/purchase-indent/multi-vendor')) return 'Create Purchase Orders for Purchase Indent';

    return null;
  };

  useEffect(() => {
    if (user && !isUserReady) {
      if (user && !cartDataV2) {
        getCartV2({ tenantId: user?.tenant_info?.tenant_id });
      }
      if (user && !isUserReady) {
        setIsUserReady(true);
        setSelectedTenant(user?.tenant_info?.tenant_id);
      }
      if (org?.organisation?.[0]?.org_id && !config) {
        setConfig(org?.organisation?.[0]?.config);
      }
    }
  }, []);

  useEffect(() => {
    if (user) {
      getCartV2({ tenantId: user?.tenant_info?.tenant_id });
    }
  }, [call]);

  const getUserInitials = () => {
    const nameKeywords = user?.first_name?.trim()?.split(' ') || [];

    // Filter out empty strings and undefined values
    const validKeywords = nameKeywords.filter(Boolean);

    if (validKeywords.length > 1) {
      return `${validKeywords[0][0] || ''}${validKeywords[1][0] || ''
        }`.toUpperCase();
    }

    if (validKeywords.length === 1) {
      return `${validKeywords[0][0] || ''}${validKeywords[0][1] || ''
        }`.toUpperCase();
    }

    return 'HD'; // Fallback initials
  };

  const onOpenChange = (keys) => {
    const key = keys.pop();
    setOpenKeys([key]);
  };

  const findPageName = () => {
    if (match.path.includes('/inventory/manage')) return Pages.product.list;
    if (match.path.includes('/inventory/product/view/'))
      return Pages.product.view;
    if (match.path.includes('/inventory/indent/create')) return Pages.sa.create;
    if (match.path.includes('/inventory/indent/update')) return Pages.sa.update;
    if (match.path.includes('/inventory/indent')) return Pages.sa.list;
    if (match.path.includes('/inventory/stock-transfer/create'))
      return Pages.st.create;
    if (match.path.includes('/inventory/stock-transfer/update'))
      return Pages.st.update;
    if (match.path.includes('/inventory/stock-transfer/view'))
      return Pages.st.view;
    if (match.path.includes('/inventory/stock-transfer')) return Pages.st.list;
    if (match.path.includes('/inventory/delivery-challan/view'))
      return Pages.dc.view;
    if (match.path.includes('/inventory/delivery-challan'))
      return Pages.dc.list;

    // Purchase
    if (match.path.includes('/vendors/view')) return Pages.vendor.view;
    if (match.path.includes('/purchase/vendors')) return Pages.vendor.list;

    // Purchase Requests
    if (match.path.includes('/purchase/purchase-request/create'))
      return Pages.pr.create;
    if (match.path.includes('/purchase/purchase-request/update'))
      return Pages.pr.update;
    if (match.path.includes('/purchase/purchase-request/view'))
      return Pages.pr.view;
    if (match.path.includes('/purchase/purchase-request')) return Pages.pr.list;

    // Purchase Indents
    if (match.path.includes('/purchase/purchase-indent/create')) return Pages.pi.create;
    if (match.path.includes('/purchase/purchase-indent/update')) return Pages.pi.update;
    if (match.path.includes('/purchase/purchase-indent/view')) return Pages.pi.view;
    if (match.path.includes('/purchase/purchase-indent')) return Pages.pi.list;

    // Purchase Orders
    if (match.path.includes('/purchase/purchase-orders/create'))
      return Pages.po.create;
    if (match.path.includes('/purchase/purchase-orders/update'))
      return Pages.po.update;
    if (match.path.includes('/approval')) return Pages.po.view;
    if (match.path.includes('/purchase/purchase-orders')) return Pages.po.list;

    // Goods Receiving Notes
    if (match.path.includes('/purchase/goods-receiving/create'))
      return Pages.grn.create;
    if (match.path.includes('/purchase/goods-receiving/update'))
      return Pages.grn.update;
    if (match.path.includes('/purchase/goods-receiving/view'))
      return Pages.grn.view;
    if (match.path.includes('/purchase/goods-receiving')) return Pages.grn.list;

    // Accounts Payable Invoice
    if (match.path.includes('/purchase/account-payable-invoice/create'))
      return Pages.api.create;
    if (match.path.includes('/purchase/account-payable-invoice/update'))
      return Pages.api.update;
    if (match.path.includes('/purchase/account-payable-invoice/view'))
      return Pages.api.view;
    if (match.path.includes('/purchase/account-payable-invoice'))
      return Pages.api.list;

    // Debit Notes
    if (match.path.includes('/purchase/debit-notes/create'))
      return Pages.dn.create;
    if (match.path.includes('/purchase/debit-notes/update'))
      return Pages.dn.update;
    if (match.path.includes('/purchase/debit-note/view')) return Pages.dn.view;
    if (match.path.includes('/purchase/debit-notes')) return Pages.dn.list;

    // Expense
    if (match.path.includes('/purchase/expense/create')) return Pages.em.create;
    if (match.path.includes('/purchase/expense/update')) return Pages.em.update;
    if (match.path.includes('/purchase/expense/view')) return Pages.em.view;
    if (match.path.includes('/purchase/expense')) return Pages.em.list;

    // Production
    if (match.path.includes('/production/manufacturing-orders/create'))
      return Pages.mo.create;
    if (match.path.includes('/production/manufacturing-orders/update'))
      return Pages.mo.update;
    if (match.path.includes('/production/manufacturing-orders/view'))
      return Pages.mo.view;
    if (match.path.includes('/production/manufacturing-orders'))
      return Pages.mo.list;
    if (match.path.includes('/production/bom')) return Pages.bom.list;

    // Quality
    if (match.path.includes('/quality/quality-checks')) return Pages.qc.list;
    if (match.path.includes('/quality/quality-control-points'))
      return Pages.qcp.list;

    // Sales
    if (match.path.includes('/sales/customer/view')) return Pages.customer.view;
    if (match.path.includes('/sales/customers')) return Pages.customer.list;
    if (match.path.includes('/sales/sales-orders/create'))
      return Pages.so.create;
    if (match.path.includes('/sales/sales-orders/update'))
      return Pages.so.update;
    if (match.path.includes('/sales/sales-order/view')) return Pages.so.view;
    if (match.path.includes('/sales/sales-orders')) return Pages.so.list;
    if (match.path.includes('/sales/invoices/create'))
      return Pages.invoice.create;
    if (match.path.includes('/sales/invoices/update'))
      return Pages.invoice.update;
    if (match.path.includes('/sales/invoice/view')) return Pages.invoice.view;
    if (match.path.includes('/sales/invoices')) return Pages.invoice.list;
    if (match.path.includes('/sales/credit-notes/create'))
      return Pages.cn.create;
    if (match.path.includes('/sales/credit-notes/update'))
      return Pages.cn.update;
    if (match.path.includes('/sales/credit-note/view')) return Pages.cn.view;
    if (match.path.includes('/sales/credit-notes')) return Pages.cn.list;

    // Settings
    if (match.path.includes('/reports')) return Pages.reports.onlyPage;
    if (match.path.includes('/settings/team')) return Pages.user.list;
    if (match.path.includes('/settings/workflow')) return Pages.workflow.list;
    if (match.path.includes('/settings/business-profile'))
      return Pages.companyProfile.onlyPage;

    // Admin
    if (match.path.includes('/admin/business-units')) return AdminPages.bu.list;
    if (match.path.includes('/admin/inventory')) return AdminPages.product.list;
    if (match.path.includes('/admin/vendors')) return AdminPages.vendor.list;
    if (match.path.includes('/admin/customers'))
      return AdminPages.customer.list;
    if (match.path.includes('/admin/configuration'))
      return AdminPages.configuration.onlyPage;

    // pending actions
    if (match.path.includes('/actions/pending-purchase-requests'))
      return Pages.pendingPr.onlyPage;
    if (match.path.includes('/actions/pending-purchase-orders'))
      return Pages.pendingPo.onlyPage;
    if (match.path.includes('/actions/pending-invoices'))
      return Pages.pendingInvoice.onlyPage;
    if (match.path.includes('/actions/pending-estimates'))
      return Pages.pendingSo.onlyPage;
    if (match.path.includes('/actions/pending-sales-orders'))
      return Pages.pendingSo.onlyPage;
    if (match.path.includes('/actions/pending-payment-approvals'))
      return Pages.pendingPayment.onlyPage;

    // Bulk Upload
    if (match.path.includes('/analytics/bulkUpload'))
      return Pages.bulkUpload.list;

    // home
    if (match.path.includes('/')) return Pages.home.onlyPage;
  };

  const goBack = (url) => {
    if (url.includes('/analytics/reports/')) {
      history.push('/analytics/reports');
    } else history.goBack();
  };

  const showHelpButton = () => {
    if (
      match.path.includes('/production/bom') ||
      (match.path.includes('inventory/product/view/') &&
        window?.location?.href?.includes('?tab=3'))
    )
      return {
        visible: true,
        url: 'https://scribehow.com/page/_Production_Management_Usage_Guide__bTr3BxjgTbmGkk9zy_1s5A?referrer=documents',
      };
    if (
      match.path.includes('/production/manufacturing-orders') ||
      match.path.includes('production/manufacturing-orders')
    )
      return {
        visible: true,
        url: 'https://scribehow.com/page/_Production_Management_Usage_Guide__bTr3BxjgTbmGkk9zy_1s5A?referrer=documents',
      };
    // if (match.path.includes('/production/planning')) return { visible: true, url: 'https://scribehow.com/page/_Production_Management_Usage_Guide__bTr3BxjgTbmGkk9zy_1s5A?referrer=documents' };
    if (match.path.includes('/production/job-works'))
      return {
        visible: true,
        url: 'https://scribehow.com/page/_Production_Management_Usage_Guide__bTr3BxjgTbmGkk9zy_1s5A?referrer=documents',
      };
    if (match.path.includes('/production/configurations'))
      return {
        visible: true,
        url: 'https://scribehow.com/page/_Production_Management_Usage_Guide__bTr3BxjgTbmGkk9zy_1s5A?referrer=documents',
      };
    // if (match.path.includes('/settings/warehouse-setup')) return { visible: true, url: 'https://help.procuzy.com/getting-started/ms8ogPGg2SdSTZtDfxY84C/setting-up-warehouse-racks-sections-and-shelves/eU1M7NF7MYmAhsjXmEWYkG' };
    // purchase
    if (match.path.includes('/purchase/vendors/manage')) return { visible: true, url: 'https://scribehow.com/page/_Purchase_Management_Usage_Guide__GKpOc6WsQYyOJ03lGmWEiw' };
    if (match.path.includes('/purchase/purchase-request/view')) return { visible: true, url: 'https://scribehow.com/page/_Purchase_Management_Usage_Guide__GKpOc6WsQYyOJ03lGmWEiw' };
    if (match.path.includes('/purchase/purchase-request')) return { visible: true, url: 'https://scribehow.com/page/_Purchase_Management_Usage_Guide__GKpOc6WsQYyOJ03lGmWEiw' };
    if (match.path.includes('/purchase/purchase-indent/view')) return { visible: true, url: 'https://scribehow.com/page/_Purchase_Management_Usage_Guide__GKpOc6WsQYyOJ03lGmWEiw' };
    if (match.path.includes('/purchase/purchase-indent')) return { visible: true, url: 'https://scribehow.com/page/_Purchase_Management_Usage_Guide__GKpOc6WsQYyOJ03lGmWEiw' };
    if (match.path.includes('/purchase/rfq')) return { visible: true, url: 'https://scribehow.com/page/_Purchase_Management_Usage_Guide__GKpOc6WsQYyOJ03lGmWEiw' };
    if (match.path.includes('/purchase/purchase-orders')) return { visible: true, url: 'https://scribehow.com/page/_Purchase_Management_Usage_Guide__GKpOc6WsQYyOJ03lGmWEiw' };
    if (match.path.includes('/purchase/goods-receiving')) return { visible: true, url: 'https://scribehow.com/page/_Purchase_Management_Usage_Guide__GKpOc6WsQYyOJ03lGmWEiw' };
    if (match.path.includes('/purchase/debit-notes')) return { visible: true, url: 'https://scribehow.com/page/_Purchase_Management_Usage_Guide__GKpOc6WsQYyOJ03lGmWEiw' };
    if (match.path.includes('/purchase/vendor-payouts')) return { visible: true, url: 'https://scribehow.com/page/_Purchase_Management_Usage_Guide__GKpOc6WsQYyOJ03lGmWEiw' };
    if (match.path.includes('/purchase/expense')) return { visible: true, url: 'https://scribehow.com/page/_Purchase_Management_Usage_Guide__GKpOc6WsQYyOJ03lGmWEiw' };
    // inventory
    if (match.path.includes('/inventory/manage'))
      return {
        visible: true,
        url: 'https://scribehow.com/page/Inventory_Management_Usage_Guide__j2yyZ399QdyR8i2iWB2s9w?referrer=documents',
      };
    if (match.path.includes('/inventory/indent'))
      return {
        visible: true,
        url: 'https://scribehow.com/page/Inventory_Management_Usage_Guide__j2yyZ399QdyR8i2iWB2s9w?referrer=documents',
      };
    if (match.path.includes('/inventory/stock-transfer'))
      return {
        visible: true,
        url: 'https://scribehow.com/page/Inventory_Management_Usage_Guide__j2yyZ399QdyR8i2iWB2s9w?referrer=documents',
      };
    if (match.path.includes('/settings/team'))
      return {
        visible: true,
        url: 'https://scribehow.com/page/_Account_Configurations__tWat-siVS9SUTIbaUQ4Lig?referrer=documents',
      };
    if (match.path.includes('/settings/workflow'))
      return {
        visible: true,
        url: 'https://scribehow.com/page/_Account_Configurations__tWat-siVS9SUTIbaUQ4Lig?referrer=documents',
      };
    if (match.path.includes('/settings/business-profile'))
      return {
        visible: true,
        url: 'https://scribehow.com/page/_Account_Configurations__tWat-siVS9SUTIbaUQ4Lig?referrer=documents',
      };
    // QC
    if (match.path.includes('/quality/quality-control-points'))
      return {
        visible: true,
        url: 'https://scribehow.com/page/_Quality_Control__Integration_Guide__I4TvTo_3Qr-QlR0jzOhGHQ?referrer=documents',
      };
    if (match.path.includes('/inventory/quality-checks'))
      return {
        visible: true,
        url: 'https://scribehow.com/page/_Quality_Control__Integration_Guide__I4TvTo_3Qr-QlR0jzOhGHQ?referrer=documents',
      };
    // sales
    if (match.path.includes('/sales/sales-orders'))
      return {
        visible: true,
        url: 'https://scribehow.com/page/Sales_Management_Usage_Guide__PGTsNafQSlewy3IFCmP59w?referrer=document',
      };
    if (match.path.includes('/sales/invoices'))
      return {
        visible: true,
        url: 'https://scribehow.com/page/Sales_Management_Usage_Guide__PGTsNafQSlewy3IFCmP59w?referrer=document',
      };
    if (match.path.includes('/sales/customers'))
      return {
        visible: true,
        url: 'https://scribehow.com/page/Sales_Management_Usage_Guide__PGTsNafQSlewy3IFCmP59w?referrer=document',
      };
    if (match.path.includes('/sales/incoming-payments'))
      return {
        visible: true,
        url: 'https://scribehow.com/page/Sales_Management_Usage_Guide__PGTsNafQSlewy3IFCmP59w?referrer=document',
      };

    return { visible: false, url: '' };
  };

  const getEntityCreateUrl = (entityName) => {
    switch (entityName) {
    case 'PURCHASE_ORDER': {
      return '/purchase/purchase-orders/create';
    }
    case 'PURCHASE_REQUEST': {
      return '/purchase/purchase-request/create';
    }
    case 'PURCHASE_INDENT': {
      return '/purchase/purchase-indent/create';
    }
    case 'GOOD_RECEIVING': {
      return '/purchase/goods-receiving/create';
    }
    case 'ACCOUNT_PAYABLE_INVOICE': {
      return '/purchase/account-payable-invoice/create';
    }
    case 'VENDORS': {
      return '/purchase/vendors/manage?page=1&limit=10&toCreate=true';
    }
    case 'SALES_ESTIMATE': {
      return '/sales/estimate/create';
    }
    case 'SALES_ORDER': {
      return '/sales/sales-orders/create';
    }
    case 'PACKING_SLIP': {
      return '/sales/packing-slips/create';
    }
    case 'INVOICE': {
      return '/sales/invoices/create';
    }
    case 'CONSUMPTION_ORDER': {
      return '/sales/consumption/create';
    }
    case 'CONSUMPTION': {
      return '/sales/consumption/create';
    }
    case 'CREDIT_NOTE': {
      return '/sales/credit-notes/create';
    }
    case 'CUSTOMER': {
      return '/sales/customers?page=1&limit=10&toCreate=true';
    }
    case 'PRODUCT': {
      return '/inventory/manage?page=1&limit=25&sort_by=alter_id_desc&toCreate=true';
    }
    case 'STOCK_ADJUSTMENT': {
      return '/inventory/indent/create';
    }
    case 'STOCK_TRANSFER': {
      return '/inventory/stock-transfer/create';
    }
    case 'QUALITY_RULES': {
      return '/quality/quality-control-points?page=1&limit=10&toCreate=true';
    }
    case 'EXPENSES': {
      return '/purchase/expense?page=1&limit=10&toCreate=true';
    }
    case 'VENDOR_PAYOUT': {
      return '/payments/create';
    }
    case 'CUSTOMER_PAYMENTS': {
      return '/sales/create-incoming-payments';
    }
    case 'BOM': {
      return '/production/bom/create';
    }
    case 'MO': {
      return '/production/manufacturing-orders/create';
    }
    case 'MACHINE': {
      return '/production/configurations?page=1&limit=10&toCreate=true';
    }
    case 'PRODUCTION_PROCESS': {
      return '/production/configurations?tab=/production-processes&page=1&limit=10&toCreate=true';
    }
    case 'REQUEST_FOR_QUOTATION': {
      return '/purchase/rfq/create';
    }
    case 'GATE_DOCUMENT': {
      return '/logistic/gate-document?page=1&limit=10&toCreate=true';
    }
    default: {
      return '/purchase/purchase-request/create';
    }
    }
  };
  const getPermission = (entityName) => {
    switch (entityName) {
    // purchase permissions
    case 'VENDORS': {
      return Helpers.getPermission(Helpers.permissionEntities.VENDORS, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.is_active;
    }

    case 'PURCHASE_REQUEST': {
      return Helpers.getPermission(Helpers.permissionEntities.PURCHASE_REQUEST, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.purchase_config?.sub_modules?.purchase_request?.is_active;
    }

    case 'PURCHASE_INDENT': {
      return Helpers.getPermission(Helpers.permissionEntities.PURCHASE_INDENT, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.purchase_config?.sub_modules?.purchase_indent?.is_active;
    }

    case 'PURCHASE_ORDER': {
      return Helpers.getPermission(Helpers.permissionEntities.PURCHASE_ORDER, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.is_active;
    }

    case 'GOOD_RECEIVING': {
      return Helpers.getPermission(Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.is_active;
    }

    case 'ACCOUNT_PAYABLE_INVOICE': {
      return Helpers.getPermission(Helpers.permissionEntities.ACCOUNT_PAYABLE_INVOICE, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.is_active && user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_enabled;
    }

    case 'REQUEST_FOR_QUOTATION': {
      return Helpers.getPermission(Helpers.permissionEntities.REQUEST_FOR_QUOTATION, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.purchase_config?.addons?.request_for_quotation?.is_active;
    }

    // sales permissions
    case 'CUSTOMER': {
      return Helpers.getPermission(Helpers.permissionEntities.CUSTOMER, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.sales_config?.sub_modules?.customer?.is_active;
    }

    case 'SALES_ESTIMATE': {
      return Helpers.getPermission(Helpers.permissionEntities.SALES_ESTIMATE, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.sales_config?.sub_modules?.sales_estimate?.is_active;
    }

    case 'SALES_ORDER': {
      return Helpers.getPermission(Helpers.permissionEntities.SALES_ORDER, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.sales_config?.sub_modules?.sales_order?.is_active;
    }

    case 'PACKING_SLIP': {
      return Helpers.getPermission(Helpers.permissionEntities.PACKING_SLIP, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.sales_config?.sub_modules?.packing_slip?.is_active;
    }

    case 'INVOICE': {
      return Helpers.getPermission(Helpers.permissionEntities.INVOICE, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.sales_config?.sub_modules?.invoice?.is_active;
    }

    case 'CONSUMPTION_ORDER': {
      return Helpers.getPermission(Helpers.permissionEntities.CONSUMPTION_ORDER, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.sales_config?.sub_modules?.invoice?.is_active;
    }

    case 'CREDIT_NOTE': {
      return Helpers.getPermission(Helpers.permissionEntities.CREDIT_NOTE, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.sales_config?.sub_modules?.credit_note?.is_active;
    }

    // inventory permissions
    case 'PRODUCT': {
      return Helpers.getPermission(Helpers.permissionEntities.PRODUCT, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.inventory_config?.sub_modules?.product?.is_active;
    }

    case 'STOCK_ADJUSTMENT': {
      return Helpers.getPermission(Helpers.permissionEntities.INVENTORY_INDENT, Helpers.permissionTypes.CREATE, user);
    }

    case 'STOCK_TRANSFER': {
      return Helpers.getPermission(Helpers.permissionEntities.STOCK_TRANSFER, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.inventory_config?.sub_modules?.stock_transfer?.is_active;
    }

    case 'DELIVERY_CHALLAN': {
      return Helpers.getPermission(Helpers.permissionEntities.DELIVERY_CHALLAN, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.inventory_config?.sub_modules?.delivery_challan?.is_active;
    }

    // quality control permissions
    case 'QUALITY_RULES': {
      return Helpers.getPermission(Helpers.permissionEntities.QUALITY_RULES, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.quality_control_config?.sub_modules?.quality_control_rule?.is_active;
    }

    // production permissions
    case 'BOM': {
      return Helpers.getPermission(Helpers.permissionEntities.BOM, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.production_config?.sub_modules?.bill_of_material?.is_active;
    }

    case 'MO': {
      return Helpers.getPermission(Helpers.permissionEntities.MO, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.production_config?.sub_modules?.manufacturing_order?.is_active;
    }

    case 'MACHINE':
    case 'PRODUCTION_PROCESS': {
      return Helpers.getPermission(Helpers.permissionEntities.FACTORY_SETUP, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.production_config?.addons?.factory_setup?.is_active;
    }

    // payment permissions
    case 'VENDOR_PAYOUT': {
      return Helpers.getPermission(Helpers.permissionEntities.VENDOR_PAYOUT, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.purchase_config?.sub_modules?.payment_outgoing?.is_active;
    }

    case 'CUSTOMER_PAYMENTS': {
      return Helpers.getPermission(Helpers.permissionEntities.CUSTOMER_PAYMENTS, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.sales_config?.sub_modules?.payment_incoming?.is_active;
    }

    case 'EXPENSES': {
      return Helpers.getPermission(Helpers.permissionEntities.EXPENSES, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.purchase_config?.addons?.expense?.is_active;
    }

    // logistics
    case 'GATE_DOCUMENT': {
      return Helpers.getPermission(Helpers.permissionEntities.GATE_DOCUMENT, Helpers.permissionTypes.CREATE, user)
          && user?.tenant_info?.logistics_config?.sub_modules?.gate_document?.is_active;
    }

    default: {
      return false;
    }
    }
  };

  const getNameToBeShown = (entityName) => {
    if (
      [
        'MACHINE',
        'PRODUCTION_PROCESS',
        'STOCK_ADJUSTMENT',
        'SALES_ESTIMATE',
      ].includes(entityName)
    ) {
      return `+ ${entityName.toProperCase().replaceAll('_', ' ')}`;
    }
    if (entityName === 'BOM') {
      return '+ Bill of Material';
    }
    if (entityName === 'MO') {
      return '+ Manufacturing Order';
    }
    if (entityName === 'SALES_ESTIMATE') {
      return `+ ${documentUINames.estimateUIName}`;
    }
    if (entityName === 'PURCHASE_REQUEST') {
      return '+ Material Request';
    }
    return `+ ${Helpers.permissionEntities[entityName]
      .toProperCase()
      .replaceAll('_', ' ')}`;
  };
  const getEntityIcon = (entity) => {
    if (entity === 'PURCHASE') return faCartShopping;
    if (entity === 'SALES') return faBagShopping;
    if (entity === 'INVENTORY') return faTags;
    if (entity === 'MANUFACTURING') return faIndustry;
    if (entity === 'PAYMENT') return faMoneyBills;
    if (entity === 'LOGISTICS') return faWarehouse;
  };
  const permissionEntities = {
    PURCHASE: ['VENDORS', 'PURCHASE_REQUEST', 'PURCHASE_INDENT', 'PURCHASE_ORDER', 'GOOD_RECEIVING', 'ACCOUNT_PAYABLE_INVOICE', 'REQUEST_FOR_QUOTATION'],
    SALES: ['CUSTOMER', 'SALES_ESTIMATE', 'SALES_ORDER', 'PACKING_SLIP', 'INVOICE', 'CONSUMPTION_ORDER', 'CREDIT_NOTE'],
    INVENTORY: ['PRODUCT', 'STOCK_ADJUSTMENT', 'STOCK_TRANSFER', 'QUALITY_RULES'],
    MANUFACTURING: ['BOM', 'MO', 'MACHINE', 'PRODUCTION_PROCESS'],
    PAYMENT: ['VENDOR_PAYOUT', 'CUSTOMER_PAYMENTS', 'EXPENSES'],
    LOGISTICS: ['GATE_DOCUMENT'],
  };
  const truePermissionEntities = {};
  Object.keys(permissionEntities).map((key) => {
    const entities = permissionEntities[key];
    entities.map((entity) => {
      if (getPermission(entity)) {
        if (truePermissionEntities[key]) {
          truePermissionEntities[key].push(entity);
        } else {
          truePermissionEntities[key] = [];
          truePermissionEntities[key].push(entity);
        }
      }
    });
  });

  const UserMenuConfig = JSON.parse(localStorage.getItem('user_menu_config'));

  const checkForPermissions = (permissionsEntities, addonPermissionCheck) => {
    let hasPermission = false;

    if (permissionsEntities.length === 0) {
      hasPermission = true;
    }

    permissionsEntities.forEach((permissionEntity) => {
      if (
        Helpers.getPermission(
          permissionEntity,
          Helpers.permissionTypes.READ,
          user
        )
      ) {
        hasPermission = true;
      }
    });

    if (addonPermissionCheck) {
      if (addonPermissionCheck?.condition_type === 'OR') {
        hasPermission = hasPermission || addonPermissionCheck?.has_permission;
      } else {
        hasPermission = hasPermission && addonPermissionCheck?.has_permission;
      }
    }

    return hasPermission;
  };

  const visibleMenus = ({ menus }) => {
    const menuItems = [];

    menus.forEach((menu) => {
      if (
        checkForPermissions(
          menu.permission_entity,
          menu?.addon_permission_check
        ) &&
        (menu?.isVisible ? menu?.isVisible?.onMobile : true)
      ) {
        menuItems.push(menu);
      }
    });

    return menuItems;
  };

  const renderIcons = (menu) => {
    if (!menu) return '';

    const iconDetails = menu.prefixIcon;

    if (iconDetails && iconDetails.iconLibrary && iconDetails.iconName) {
      if (iconDetails.iconLibrary === iconLibrary.FONT_AWESOME_ICON) {
        return <FontAwesomeIcon icon={iconDetails.iconName} />;
      }
      if (iconDetails.iconLibrary === iconLibrary.ANT_DESIGN_ICON) {
        return iconDetails.iconName;
      }
    }

    return '';
  };

  return (
    <Fragment>
      <Drawer
        placement="left"
        onClose={() => setMobileSlider(!mobileSlider)}
        open={mobileSlider}
        destroyOnClose
        width={270}
        className="mobile__side-menu-drawer"
      >
        <div
          className="mobile__side-menu"
          onClick={() => setMobileSlider(false)}
        >
          {!user?.tenant_info?.global_config?.settings?.white_label_platform ? (
            <H3Image src={logo} className="mobile__side-menu-image" />
          ) : (
            <div className="mobile__side-menu__logo-wl">
              <div className="mobile__side-menu__logo-image-left-wrapper">
                <H3Image
                  src={org?.organisation?.[0]?.logo}
                  className="mobile__side-menu__logo-image-left-mg__26"
                />
              </div>
              <div className="mobile__side-menu__logo-image-right-wrapper">
                <div className="mobile__side-menu__logo-image-right-header">
                  Powered by
                </div>
                <H3Image
                  src={logo}
                  className="mobile__side-menu__logo-image-right"
                />
              </div>
            </div>
          )}
          <FontAwesomeIcon
            className="mobile__side-menu-icon"
            icon={faAnglesLeft}
            onClick={() => {
              setMobileSlider(!mobileSlider);
            }}
          />
        </div>

        <div className="mobile-menu__wrapper">
          <Menu
            className="mobile__side-menu-list"
            selectedKeys={[currentPage]}
            defaultSelectedKeys={['/']}
            theme="light"
            onOpenChange={onOpenChange}
            openKeys={openKeys}
            onClick={(value) => {
              history.push(value.key);
            }}
            mode="inline"
          >
            {visibleMenus({
              menus: Object.values(userMenuConstant(user).menu).sort(
                (a, b) => Number(a.seq) - Number(b.seq)
              ),
            }).map((menu) => {
              if (menu.children && menu.children.length) {
                return (
                  <SubMenu
                    key={menu.key}
                    icon={renderIcons(menu)}
                    title={menu.label}
                  >
                    {visibleMenus({ menus: menu.children }).map((childMenu) => (
                      <Menu.Item
                        key={childMenu.key}
                        icon={renderIcons(childMenu)}
                      >
                        <div className="user-menu__title-wrapper">
                          <span>{childMenu.label}</span>
                          {childMenu.is_premium && (
                            <H3Image src={Crown} className="user-menu__crown" />
                          )}
                        </div>
                      </Menu.Item>
                    ))}
                  </SubMenu>
                );
              }
              return (
                <Menu.Item key={menu.key} icon={renderIcons(menu)}>
                  <div className="user-menu__title-wrapper">
                    <span>{menu.label}</span>
                  </div>
                </Menu.Item>
              );
            })}
          </Menu>

          <Menu
            inlineCollapsed={false}
            theme="light"
            mode="inline"
            style={{
              padding: '0px !important',
              marginTop: 'auto',
            }}
            className="user-menu__bottom"
          >
            <Menu.Item
              key="whatsapp-new"
              icon={
                <img
                  src={WhatsApp}
                  alt="WhatsApp"
                  style={{ width: '17px', height: '17px', cursor: 'pointer' }}
                  onClick={(e) => {
                    e.stopPropagation();
                    window.open(
                      'https://www.whatsapp.com/channel/0029Vb1fE9RD8SE36GMYEV0S'
                    );
                  }}
                />
              }
              onClick={() =>
                window.open(
                  'https://www.whatsapp.com/channel/0029Vb1fE9RD8SE36GMYEV0S'
                )
              }
            >
              <div
                style={{
                  position: 'relative',
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                What's new in Procuzy?
                <span id="fb-update-badge" />
              </div>
            </Menu.Item>
            <Menu.Item
              key="/contact"
              icon={<FontAwesomeIcon icon={faMessage} />}
              onClick={() => {
                // window.Intercom('show');
                globalThis.Featurebase('show');
              }}
              className="chat-button"
            >
              Chat with us
            </Menu.Item>
          </Menu>
        </div>
      </Drawer>
      {/* <div className="header__wrapper-outer"> */}
      {/* header ---------------------------------------------------------------------------------------------------------- */}
      <div
        className={
          UserMenuConfig === 'FIXED'
            ? 'header__wrapper-outer'
            : 'header__wrapper-outer-collapsible'
        }
      >
        <div
          className={
            UserMenuConfig === 'FIXED'
              ? 'header__wrapper-fixed'
              : 'header__wrapper__collapsed'
          }
        >
          {getPageName() ? (
            <div className="header__page-name__collapsed">
              {(!match.path.includes('/admin') ||
                match.path.includes('/integration/')) && (
                <H3Image
                  src={rightArrow}
                  className="header__page-name__back-icon"
                  onClick={() => {
                    goBack(match.path);
                  }}
                />
              )}
              <H3Text
                text={getPageName()}
                className="header__page-name__text"
              />
            </div>
          ) : (
            <H3Text
              text={`Hi, ${user?.first_name} ${user?.last_name ? user?.last_name : ''
                } 👋`}
              className="header__page-name__greeting"
            />
          )}
          {!getPageName() && (
            <Fragment>
              <div className="header__mobile__logo__wrapper">
                <H3Image src={logo} className="header__mobile__logo-image" />
              </div>
            </Fragment>
          )}
          <div className="header-right">
            {(orgIsActive || differenceInDays >= -30) &&
              !history.location.pathname.includes('/admin/') && (
              <React.Fragment>
                <GlobalSearch />
                <Dropdown
                  overlay={
                    <div className="quick-actions__list-wrapper hide__in-mobile">
                      {Object.keys(truePermissionEntities).map((key) => (
                        <div key={key}>
                          <div className="quick-actions__list-title">
                            <FontAwesomeIcon
                              color="#2D7DF7"
                              icon={getEntityIcon(key)}
                            />
                            <span>{key.toProperCase()}</span>
                          </div>
                          {truePermissionEntities[key].map(
                            (entity) =>
                              getPermission(entity) && (
                                <div
                                  className="quick-actions__list-item-wrapper"
                                  key={entity}
                                >
                                  <Link to={getEntityCreateUrl(entity)}>
                                    <H3Text
                                      text={getNameToBeShown(
                                        entity
                                      )?.toProperCase()}
                                      className="quick-actions__list-item"
                                    />
                                  </Link>
                                </div>
                              )
                          )}
                        </div>
                      ))}
                    </div>
                  }
                  trigger={['click']}
                  placement="bottomLeft"
                >
                  <div className="org-inv__wrapper-home quick-actions__cta hide__in-mobile">
                    <H3Text
                      text="+"
                      className="quick-actions__cta-text"
                      id="quick-create-button"
                    />
                  </div>
                </Dropdown>
              </React.Fragment>
            )}
            {orgIsActive &&
              (Helpers.getPermission(
                Helpers.permissionEntities.PURCHASE_REQUEST,
                Helpers.permissionTypes.CREATE,
                user
              ) ||
                Helpers.getPermission(
                  Helpers.permissionEntities.PURCHASE_ORDER,
                  Helpers.permissionTypes.CREATE,
                  user
                )) &&
              !history.location.pathname.includes('/admin/') && (
              <div
                className="header-right__help"
                onClick={() => history.push('/inventory/preview-cart')}
                style={{
                  position: 'relative',
                  backgroundColor: 'rgb(234, 241, 254)',
                  color: 'rgb(64, 150, 255)',
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                {Number(cartDataV2?.count) > 0 && (
                  <div
                    style={{
                      position: 'absolute',
                      right: '-2px',
                      top: '-4px',
                      backgroundColor: 'red',
                      color: 'white',
                      borderRadius: '50%',
                      width: '17px',
                      height: '17px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '10px',
                    }}
                  >
                    {cartDataV2?.count}
                  </div>
                )}
                <FontAwesomeIcon icon={faCartShopping} className="tb-blue" />
              </div>
            )}
            <KeyboardShortcutHandler />
            {showHelpButton()?.visible && (
              <div
                className="header-right__help"
                onClick={() => setShowHelpdesk(true)}
              >
                <FontAwesomeIcon icon={faVideo} />
                Need Help?
              </div>
            )}
            {!history.location.pathname.includes('/admin/') && (
              <React.Fragment>
                <Fragment>
                  <div
                    className="header__mobile__menu-icon__wrapper"
                    onClick={() => setMobileSlider(true)}
                  >
                    <MenuOutlined />
                  </div>
                </Fragment>

                <div
                  className="header-right__switch-tenant hide__in-mobile"
                  onClick={() => setShowSwitchBuDrawer(true)}
                >
                  <H3Image
                    src={userIcon}
                    className="header-right__switch-tenant-user"
                  />
                  <H3Text
                    text={`${user?.tenant_info?.tenant_name.substring(0, 25)}${user?.tenant_info?.tenant_name?.length > 25 ? '...' : ''
                      }`}
                    className="header-right__switch-tenant-name hide__in-mobile"
                  />
                  <CaretDownOutlined />
                </div>
              </React.Fragment>
            )}
          </div>
        </div>
      </div>

      {/* footer ---------------------------------------------------------------------------------------------------------- */}
      <div className="footer__wrapper-outer">
        <div className="footer__wrapper-fixed">
          <div className="footer-right">
            <FontAwesomeIcon
              icon={faHome}
              onClick={() => {
                history.push('/');
              }}
              color={history.location.pathname === '/' ? '#2D7DF7' : 'black'}
            />

            {orgIsActive &&
              (Helpers.getPermission(
                Helpers.permissionEntities.PURCHASE_REQUEST,
                Helpers.permissionTypes.CREATE,
                user
              ) ||
                Helpers.getPermission(
                  Helpers.permissionEntities.PURCHASE_ORDER,
                  Helpers.permissionTypes.CREATE,
                  user
                )) &&
              !history.location.pathname.includes('/admin/') && (
              <div
                className="footer-right__help"
                onClick={() => history.push('/inventory/preview-cart')}
                style={{
                  position: 'relative',
                  color: `${history.location.pathname === '/inventory/preview-cart'
                    ? '#2D7DF7'
                    : 'black'
                      }`,
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                {Number(cartDataV2?.count) > 0 && (
                  <div
                    style={{
                      position: 'absolute',
                      right: '-8px',
                      top: '-8px',
                      backgroundColor: 'red',
                      color: 'white',
                      borderRadius: '50%',
                      width: '17px',
                      height: '17px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '10px',
                    }}
                  >
                    {cartDataV2?.count}
                  </div>
                )}
                <FontAwesomeIcon
                  icon={faCartShopping}
                  className={Number(cartDataV2?.count) > 0 ? 'tb-blue' : ''}
                />
              </div>
            )}

            {!history.location.pathname.includes('/admin/') && (
              <React.Fragment>
                <div
                  className="footer-right__switch-tenant"
                  onClick={() => setShowSwitchBuDrawer(true)}
                >
                  <H3Image
                    src={userIcon}
                    className="footer-right__switch-tenant-user"
                  />
                  <CaretDownOutlined />
                </div>
              </React.Fragment>
            )}
          </div>
        </div>
      </div>

      <Drawer
        open={showHelpdesk}
        onClose={() => setShowHelpdesk(true)}
        width="600px"
        destroyOnClose
        mask={false}
        className="header-right__help-drawer"
      >
        <div className="custom-drawer__header-wrapper">
          <div className="custom-drawer__header" style={{ width: '600px' }}>
            <H3Text
              text="How can we help? 👋"
              className="custom-drawer__title"
            />
            <div
              className="custom-drawer__close-icon"
              onClick={() => {
                setShowHelpdesk(false);
                setHelpdeskLoaded(false);
              }}
            >
              <FontAwesomeIcon icon={faCircleXmark} />
            </div>
          </div>
        </div>
        <iframe
          src={showHelpButton()?.url}
          onLoad={() => setHelpdeskLoaded(true)}
        />
        {!helpdeskLoaded && (
          <div className="header-right__help-drawer-loader">
            <LoadingOutlined />
          </div>
        )}
      </Drawer>

      <Drawer
        open={showSwitchBuDrawer}
        onClose={() => setShowSwitchBuDrawer(false)}
        width="360px"
        destroyOnClose
        maskClosable
      >
        <div className="switch-bu__wrapper">
          <div className="switch-bu__drawer-header">
            <div className="switch-bu__org-details">
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                }}
              >
                <div className="switch-bu__org-details-heading">
                  {user?.tenant_info?.organisation_name}
                </div>
                <div
                  className="mobile__close-icon"
                  onClick={() => setShowSwitchBuDrawer(false)}
                >
                  <CloseOutlined />
                </div>
              </div>
              <div className="switch-bu__org-details-text">
                {user?.tenant_info?.tenant_name}
              </div>
            </div>
            <div className="switch-bu__user-details">
              <div className="switch-bu__user-details-icon">
                {getUserInitials()}
              </div>
              <div className="switch-bu__user-details-text">
                <div className="switch-bu__user-details-name">{`${user?.first_name} ${user?.last_name}`}</div>
                <div className="switch-bu__user-details-email">
                  {user?.username}
                </div>
                <div className="switch-bu__user-details-email">
                  <span>
                    <b>{'Role - '}</b>
                  </span>
                  <span>{user?.tenant_info?.permission?.tenant_role_name}</span>
                </div>
                <div className="switch-bu__user-details-email">
                  <span>
                    <b>{'Department - '}</b>
                  </span>
                  <span>{Helpers.getTenantDepartment(user)}</span>
                </div>
                <div className="switch-bu__user-details-actions">
                  <div className="switch-bu__user-details-acc">
                    <Link to="/settings/business-profile?tab=/my-profile">
                      My Account
                    </Link>
                  </div>
                  <div
                    className="switch-bu__user-details-logout"
                    onClick={() => {
                      setShowSwitchBuDrawer(false);
                      logoutUser();
                    }}
                  >
                    Logout
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="switch-bu__list-header">
            {`My Business Units ${user?.user_tenants?.length > 1
              ? `(${user?.user_tenants?.length})`
              : ''
              }`}
          </div>
          {user?.user_tenants?.length > 4 && (
            <div className="switch-bu__list-search">
              <input
                placeholder="search business unit.."
                onChange={(event) => setSearchKeyword(event.target.value)}
              />
              <H3Image
                src={searchIcon}
                className="switch-bu__list-search-icon"
              />
            </div>
          )}
          <div className="switch-bu__list" style={{ paddingBottom: '50px' }}>
            <Radio.Group
              onChange={(event) => {
                setSelectedTenant(event?.target?.value);
                setCall(!call);
              }}
              value={selectedTenant}
            >
              {user?.user_tenants
                ?.filter(
                  (item) =>
                    item?.tenant_name
                      ?.toLowerCase()
                      ?.includes(searchKeyword?.toLowerCase()) ||
                    item?.legal_name
                      ?.toLowerCase()
                      ?.includes(searchKeyword?.toLowerCase())
                )
                ?.map((item) => (
                  <Radio
                    key={item?.tenant_id}
                    value={item?.tenant_id}
                    className={`switch-bu__item-wrapper ${item?.tenant_id === selectedTenant
                      ? 'switch-bu__item-selected-wrapper'
                      : ''
                      }`}
                  >
                    <div className="switch-bu__item">
                      <H3Text
                        text={item?.tenant_name}
                        className="switch-bu__item-name"
                      />
                      <H3Text
                        text={`${item?.city} ${item?.state}`}
                        className="switch-bu__item-address"
                      />
                    </div>
                  </Radio>
                ))}
            </Radio.Group>
          </div>
          <div className="switch-bu__button__wrapper">
            {' '}
            <div
              className="switch-bu__button"
              onClick={() => {
                switchBusinessUnit(selectedTenant, () => {
                  history.push('/');
                });
              }}
            >
              Switch Business Unit
            </div>
          </div>
        </div>
      </Drawer>
    </Fragment>
  );
}

const mapDispatchToProps = (dispatch) => ({
  logoutUser: () => dispatch(UserActions.logoutUser()),
  switchBusinessUnit: (tenantId, callback) =>
    dispatch(UserActions.switchBusinessUnit(tenantId, callback)),
  getCart: (tenantId) => dispatch(CartActions.getCart(tenantId)),
  pageAction: (page) => dispatch(TrackerActions.pageAction(page)),
  getOrg: (orgId) => dispatch(UserActions.getOrg(orgId)),
  updateOrg: (payload, callback) =>
    dispatch(UserActions.updateOrg(payload, callback)),
  getCartV2: (payload, callback) =>
    dispatch(AddToCartActions.getCartV2(payload, callback)),
});
const mapStateToProps = ({
  UserReducers,
  CartReducers,
  AddToCartReducers,
}) => ({
  user: UserReducers.user,
  cart: CartReducers.cart,
  org: UserReducers.org,
  getOrgLoading: UserReducers.getOrgLoading,
  updateOrgLoading: UserReducers.updateOrgLoading,
  cartDataV2: AddToCartReducers.cartDataV2,
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(Header));
