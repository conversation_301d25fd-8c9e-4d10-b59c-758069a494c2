import {
  Home,
  Login,
  NotFound,
  ManageProducts,
  Indent,
  CreateIndent,
  UpdateIndent,
  ManageVendors,
  VendorProfile,
  PurchaseOrders,
  CreatePurchaseOrder,
  UpdatePurchaseOrder,
  GoodsReceiving,
  GRNForm,
  ManageTeam,
  Workflow,
  AccessControl,
  MyProfile,
  ViewPurchaseOrder,
  IncomingPaymentsList,
  ViewVendorPayout,
  PreviewCart,
  PreviewPO,
  // CreatePayout,
  CreateOutgoingPayment,
  BusinessUnitsAdmin,
  VendorsAdmin,
  CustomersAdmin,
  InventoryAdmin,
  // PayoutsList,
  OutgoingPaymentsList,
  ViewReportsPage,
  AdminConfiguration,
  Customer,
  PendingPurchaseOrders,
  PendingPaymentRequests,
  OutgoingPayments,
  PendingInvoices,
  PendingGoodReceiving,
  PendingStockTransfer,
  PendingBom,
  PendingStockAdjustment,
  PendingSalesOrders,
  ListCreditNote,
  CreateCreditNote,
  UpdateCreditNote,
  ViewCreditNote,
  ListInvoices,
  CreateInvoice,
  UpdateInvoice,
  ListSalesOrders,
  CreateSalesOrder,
  CreatePackingSlip,
  ListPackingSlip,
  ViewPackingSlip,
  UpdatePackingSlip,
  UpdateSalesOrder,
  CustomerProfile,
  ViewSalesOrder,
  ViewInvoice,
  ListDebitNotePage,
  CreateDebitNotePage,
  UpdateDebitNotePage,
  ViewDebitNotePage,
  PurchaseRequestsHome,
  CreatePurchaseRequest,
  ViewPurchaseRequest,
  PRSellerSelection,
  PurchaseIndentList,
  CreatePurchaseIndent,
  UpdatePurchaseIndent,
  ViewPurchaseIndent,
  IntegrationPage,
  HelpDesk,
  PendingPurchaseRequests,
  PendingPurchaseIndent,
  PendingExpenses,
  OpenStockTransfers,
  ViewGrnPage,
  ListStockTransfer,
  CreateStockTransfer,
  UpdateStockTransfer,
  ViewStockTransfer,
  ListDeliveryChallan,
  ViewDeliveryChallan,
  CreateDeliveryChallan,
  ViewStGrnPage,
  UpdateStGrnPage,
  ListManufacturingOrders,
  CreateManufacturingOrder,
  UpdateManufacturingOrder,
  ViewManufacturingOrder,
  ViewProduct,
  CreateBulkPo,
  UpdateBulkPo,
  ViewBulkPO,
  ReportsList,
  // ReportsV2
  ReportsListV2,
  ForecastsList,
  ForecastForm,

  // Consumption
  CreateConsumption,
  ListConsumption,
  ViewConsumption,
  UpdateConsumption,

  // Production
  MrpListV2,
  ViewMrpV2,
  BOM,
  CreateBOM,
  ViewBOM,
  Configurations,
  JobWorks,
  ManualProductionEntryList,
  ManualProductionEntryForm,
  ManualProductionEntryView,

  // Quality
  QCPointList,
  ListQualityChecks,

  // Expenses
  ExpenseHome,
  ViewExpense,

  // RFQ
  RfqList,
  RfqForm,
  ViewRfq,
  PublicRfq,

  // gate pass
  ListGateDocument,
  CreateGateDocument,
  ViewGateDocument,

  // Integrations
  ZohoHome,
  ZohoSetup,
  TallyHome,
  TallySetup,
  UnicommerceHome,
  UnicommerceSetup,
  TallyIntegration,
  // Busy
  BusyHome,
  BusySetup,

  // Shopify
  ShopifyHome,
  ShopifySetup,
  ShopifyConnect,

  // Whatsapp
  WhatsappHome,
  WhatsappSetup,
  CreateIncomingPayment,

  // Settings
  WarehouseSetup,

  // E-invoicing E-waybills
  EinvoiceEwaybill,
  EwaybillForm,

  // BulkUpload
  BulkUpload,
  // Admin
  MemberAdmin,

  // Account Payable Invoice
  AccountPayableInvoiceForm,
  AccountPayableInvoiceList,
  AccountPayableInvoiceView,
  PendingAccountPayableInvoices,

  // Purchase Indent -> Multi Vendor PO
  POSellerSelectionForm
} from '../pages';

const Routes = [
  {
    path: '/',
    exact: true,
    component: Home,
    isPrivate: true,
  },
  {
    path: '/actions/pending-purchase-orders',
    exact: true,
    component: PendingPurchaseOrders,
    isPrivate: true,
  },
  {
    path: '/actions/pending-payment-approvals',
    exact: true,
    component: PendingPaymentRequests,
    isPrivate: true,
  },
  {
    path: '/actions/approved-payments',
    exact: true,
    component: OutgoingPayments,
    isPrivate: true,
  },
  {
    path: '/actions/pending-purchase-requests',
    exact: true,
    component: PendingPurchaseRequests,
    isPrivate: true,
  },
  {
    path: '/actions/pending-purchase-indents',
    exact: true,
    component: PendingPurchaseIndent,
    isPrivate: true,
  },
  {
    path: '/actions/pending-expenses',
    exact: true,
    component: PendingExpenses,
    isPrivate: true,
  },
  {
    path: '/actions/pending-estimates',
    exact: true,
    component: PendingSalesOrders,
    isPrivate: true,
  },
  {
    path: '/actions/pending-sales-orders',
    exact: true,
    component: PendingSalesOrders,
    isPrivate: true,
  },
  {
    path: '/actions/pending-invoices',
    exact: true,
    component: PendingInvoices,
    isPrivate: true,
  },
  {
    path: '/actions/pending-good-receiving',
    exact: true,
    component: PendingGoodReceiving,
    isPrivate: true,
  },
  {
    path: '/actions/pending-stock-transfer',
    exact: true,
    component: PendingStockTransfer,
    isPrivate: true,
  },
  {
    path: '/actions/pending-bom-approvals',
    exact: true,
    component: PendingBom,
    isPrivate: true,
  },
  {
    path: '/actions/pending-stock-adjustment',
    exact: true,
    component: PendingStockAdjustment,
    isPrivate: true,
  },
  {
    path: '/actions/open-stock-transfers',
    exact: true,
    component: OpenStockTransfers,
    isPrivate: true,
  },
  {
    path: '/actions/pending-account-payable-invoices',
    exact: true,
    component: PendingAccountPayableInvoices,
    isPrivate: true,
  },
  {
    path: '/login',
    exact: true,
    component: Login,
    isPrivate: false,
  },
  {
    path: '/inventory/manage',
    exact: true,
    component: ManageProducts,
    isPrivate: true,
  },
  {
    path: '/inventory/product/view/:productSkuId',
    exact: true,
    component: ViewProduct,
    isPrivate: true,
  },

  {
    path: '/inventory/preview-cart',
    exact: true,
    component: PreviewCart,
    isPrivate: true,
  },
  {
    path: '/inventory/preview-purchase-orders',
    exact: true,
    component: PreviewPO,
    isPrivate: true,
  },
  {
    path: '/inventory/indent',
    exact: true,
    component: Indent,
    isPrivate: true,
  },
  {
    path: '/inventory/indent/create',
    exact: true,
    component: CreateIndent,
    isPrivate: true,
  },
  {
    path: '/inventory/indent/update/:indentId',
    exact: true,
    component: UpdateIndent,
    isPrivate: true,
  },
  {
    path: '/inventory/indent/update/:indentId',
    exact: true,
    component: UpdateIndent,
    isPrivate: true,
  },

  // Inventory -> Delivery Challan
  {
    path: '/inventory/delivery-challan',
    exact: true,
    component: ListDeliveryChallan,
    isPrivate: true,
  },

  {
    path: '/inventory/delivery-challan/view/:dcId',
    exact: true,
    component: ViewDeliveryChallan,
    isPrivate: true,
  },

  {
    path: '/inventory/delivery-challan/create',
    exact: true,
    component: CreateDeliveryChallan,
    isPrivate: true,
  },
  {
    path: '/purchase/vendors/manage',
    exact: true,
    component: ManageVendors,
    isPrivate: true,
  },
  {
    path: '/vendors/view/:vendorId',
    exact: true,
    component: VendorProfile,
    isPrivate: true,
  },

  // Sales Menu -> Customers
  {
    path: '/sales/customers',
    exact: true,
    component: Customer,
    isPrivate: true,
  },
  {
    path: '/sales/customer/view/:customerId',
    exact: true,
    component: CustomerProfile,
    isPrivate: true,
  },

  // Sales Menu -> Sales
  {
    path: '/sales/sales-orders',
    exact: true,
    component: ListSalesOrders,
    isPrivate: true,
  },
  {
    path: '/sales/sales-orders/create',
    exact: true,
    component: CreateSalesOrder,
    isPrivate: true,
  },
  {
    path: '/sales/sales-orders/update/:orderId',
    exact: true,
    component: UpdateSalesOrder,
    isPrivate: true,
  },
  {
    path: '/sales/amc-sales-orders/create',
    exact: true,
    component: CreateSalesOrder,
    isPrivate: true,
  },
  {
    path: '/sales/amc-sales-orders/update/:orderId',
    exact: true,
    component: UpdateSalesOrder,
    isPrivate: true,
  },
  {
    path: '/sales/sales-order/view/:orderId',
    exact: true,
    component: ViewSalesOrder,
    isPrivate: true,
  },
  {
    path: '/sales/sales-order/view/:orderId/:tenantId',
    exact: true,
    component: ViewSalesOrder,
    isPrivate: true,
  },
  {
    path: '/sales/amc-sales-order/view/:orderId',
    exact: true,
    component: ViewSalesOrder,
    isPrivate: true,
  },
  {
    path: '/sales/amc-sales-order/view/:orderId/:tenantId',
    exact: true,
    component: ViewSalesOrder,
    isPrivate: true,
  },

  // Sales Menu -> Estimate
  {
    path: '/sales/estimate',
    exact: true,
    component: ListSalesOrders,
    isPrivate: true,
  },
  {
    path: '/sales/estimate/create',
    exact: true,
    component: CreateSalesOrder,
    isPrivate: true,
  },
  {
    path: '/sales/estimate/update/:orderId',
    exact: true,
    component: UpdateSalesOrder,
    isPrivate: true,
  },
  {
    path: '/sales/estimate/view/:orderId',
    exact: true,
    component: ViewSalesOrder,
    isPrivate: true,
  },
  {
    path: '/sales/estimate/view/:orderId/:tenantId',
    exact: true,
    component: ViewSalesOrder,
    isPrivate: true,
  },

  // Sales Menu -> Packing Slip
  {
    path: '/sales/packing-slips',
    exact: true,
    component: ListPackingSlip,
    isPrivate: true,
  },
  {
    path: '/sales/packing-slips/create',
    exact: true,
    component: CreatePackingSlip,
    isPrivate: true,
  },
  {
    path: '/sales/packing-slip/update/:orderId',
    exact: true,
    component: UpdatePackingSlip,
    isPrivate: true,
  },
  {
    path: '/sales/packing-slip/view/:orderId',
    exact: true,
    component: ViewPackingSlip,
    isPrivate: true,
  },
  {
    path: '/sales/packing-slip/view/:orderId/:tenantId',
    exact: true,
    component: ViewPackingSlip,
    isPrivate: true,
  },

  // Sales Menu -> Invoices
  {
    path: '/sales/invoices',
    exact: true,
    component: ListInvoices,
    isPrivate: true,
  },
  {
    path: '/sales/invoices/create',
    exact: true,
    component: CreateInvoice,
    isPrivate: true,
  },
  {
    path: '/sales/invoices/update/:invoiceId',
    exact: true,
    component: UpdateInvoice,
    isPrivate: true,
  },
  {
    path: '/sales/invoice/view/:invoiceId',
    exact: true,
    component: ViewInvoice,
    isPrivate: true,
  },
  {
    path: '/sales/invoice/view/:invoiceId/:tenantId',
    exact: true,
    component: ViewInvoice,
    isPrivate: true,
  },
  // Sales Menu -> Consumption
  {
    path: '/sales/consumption',
    exact: true,
    component: ListConsumption,
    isPrivate: true,
  },
  {
    path: '/sales/consumption/create',
    exact: true,
    component: CreateConsumption,
    isPrivate: true,
  },
  {
    path: '/sales/consumption/update/:consumptionId',
    exact: true,
    component: UpdateConsumption,
    isPrivate: true,
  },
  {
    path: '/sales/consumption/view/:consumptionId',
    exact: true,
    component: ViewConsumption,
    isPrivate: true,
  },

  // Sales Menu -> E-invoicing & E-Waybill
  {
    path: '/sales/ewaybill',
    exact: true,
    component: EinvoiceEwaybill,
    isPrivate: true,
  },
  {
    path: '/sales/ewaybill/create',
    exact: true,
    component: EwaybillForm,
    isPrivate: true,
  },
  // Sales Menu -> Credit Notes
  {
    path: '/sales/credit-notes',
    exact: true,
    component: ListCreditNote,
    isPrivate: true,
  },
  {
    path: '/sales/credit-notes/create',
    exact: true,
    component: CreateCreditNote,
    isPrivate: true,
  },
  {
    path: '/sales/credit-notes/update/:creditNoteId',
    exact: true,
    component: UpdateCreditNote,
    isPrivate: true,
  },
  {
    path: '/sales/credit-note/view/:creditNoteId',
    exact: true,
    component: ViewCreditNote,
    isPrivate: true,
  },
  {
    path: '/sales/credit-note/view/:creditNoteId/:tenantId',
    exact: true,
    component: ViewCreditNote,
    isPrivate: true,
  },
  // Sales Menu -> Return Slip
  {
    path: '/sales/return-slip/create',
    exact: true,
    component: CreateCreditNote,
    isPrivate: true,
  },
  {
    path: '/sales/return-slips',
    exact: true,
    component: ListCreditNote,
    isPrivate: true,
  },
  {
    path: '/sales/return-slip/update/:returnSlipId',
    exact: true,
    component: UpdateCreditNote,
    isPrivate: true,
  },
  {
    path: '/sales/return-slip/view/:returnSlipId',
    exact: true,
    component: ViewCreditNote,
    isPrivate: true,
  },
  {
    path: '/sales/return-slip/view/:returnSlipId/:tenantId',
    exact: true,
    component: ViewCreditNote,
    isPrivate: true,
  },
  {
    path: '/purchase/purchase-orders',
    exact: true,
    component: PurchaseOrders,
    isPrivate: true,
  },
  {
    path: '/purchase/purchase-orders/create',
    exact: true,
    component: CreatePurchaseOrder,
    isPrivate: true,
  },
  {
    path: '/purchase/bulk-purchase-orders/create',
    exact: true,
    component: CreateBulkPo,
    isPrivate: true,
  },
  {
    path: '/purchase/bulk-purchase-orders/update/:bulkPoId',
    exact: true,
    component: UpdateBulkPo,
    isPrivate: true,
  },
  {
    path: '/purchase/bulk-purchase-orders/view/:bulkPoId',
    exact: true,
    component: ViewBulkPO,
    isPrivate: true,
  },
  {
    path: '/purchase/purchase-orders/update/:poId',
    exact: true,
    component: UpdatePurchaseOrder,
    isPrivate: true,
  },
  {
    path: '/approval',
    exact: true,
    component: ViewPurchaseOrder,
    isPrivate: true,
  },
  {
    path: '/tenant/approval',
    exact: true,
    component: ViewPurchaseOrder,
    isPrivate: true,
  },
  {
    path: '/approval/payout',
    exact: true,
    component: ViewVendorPayout,
    isPrivate: true,
  },

  {
    path: '/sales/incoming-payments',
    exact: true,
    component: IncomingPaymentsList,
    isPrivate: true,
  },
  // Purchase
  {
    path: '/purchase/goods-receiving',
    exact: true,
    component: GoodsReceiving,
    isPrivate: true,
  },
  {
    path: '/purchase/goods-receiving/create',
    exact: true,
    // component: CreateGoodsReceiving,
    component: GRNForm,
    isPrivate: true,
  },
  {
    path: '/purchase/credit-notes',
    exact: true,
    component: ListCreditNote,
    isPrivate: true,
  },
  {
    path: '/purchase/goods-receiving/view/:grnId',
    exact: true,
    component: ViewGrnPage,
    isPrivate: true,
  },
  {
    path: '/purchase/goods-receiving/update/:grnId',
    exact: true,
    // component: UpdateGoodsReceiving,
    component: GRNForm,
    isPrivate: true,
  },
  {
    path: '/purchase/goods-receiving/view/:grnId/:type',
    exact: true,
    component: ViewStGrnPage,
    isPrivate: true,
  },

  {
    path: '/purchase/goods-receiving/update/:grnId/:type',
    exact: true,
    component: UpdateStGrnPage,
    isPrivate: true,
  },
  // Purchase -> Debit Notes
  {
    path: '/purchase/debit-notes',
    exact: true,
    component: ListDebitNotePage,
    isPrivate: true,
  },
  {
    path: '/purchase/debit-notes/create',
    exact: true,
    component: CreateDebitNotePage,
    isPrivate: true,
  },
  {
    path: '/purchase/debit-notes/update/:debitNoteId',
    exact: true,
    component: UpdateDebitNotePage,
    isPrivate: true,
  },
  {
    path: '/purchase/debit-note/view/:debitNoteId',
    exact: true,
    component: ViewDebitNotePage,
    isPrivate: true,
  },
  {
    path: '/purchase/expense',
    exact: true,
    component: ExpenseHome,
    isPrivate: true,
  },
  {
    path: '/purchase/expense/view/:expenseId',
    exact: true,
    component: ViewExpense,
    isPrivate: true,
  },
  {
    path: '/purchase/rfq',
    exact: true,
    component: RfqList,
    isPrivate: true,
  },
  {
    path: '/purchase/rfq/create',
    exact: true,
    component: RfqForm,
    isPrivate: true,
  },
  {
    path: '/purchase/rfq/update/:rfqId',
    exact: true,
    component: RfqForm,
    isPrivate: true,
  },
  {
    path: '/rfq',
    exact: true,
    component: PublicRfq,
    isPrivate: false,
  },
  {
    path: '/purchase/rfq/view/:rfqId',
    exact: true,
    component: ViewRfq,
    isPrivate: true,
  },

  // Inventory -> Stock Transfer
  {
    path: '/inventory/stock-transfer',
    exact: true,
    component: ListStockTransfer,
    isPrivate: true,
  },
  {
    path: '/inventory/stock-transfer/create',
    exact: true,
    component: CreateStockTransfer,
    isPrivate: true,
  },
  {
    path: '/inventory/stock-transfer/update/:stockTransferId',
    exact: true,
    component: UpdateStockTransfer,
    isPrivate: true,
  },
  {
    path: '/inventory/stock-transfer/view/:stockTransferId',
    exact: true,
    component: ViewStockTransfer,
    isPrivate: true,
  },

  {
    path: '/purchase/purchase-request',
    exact: true,
    component: PurchaseRequestsHome,
    isPrivate: true,
  },
  {
    path: '/purchase/purchase-request/create',
    exact: true,
    component: CreatePurchaseRequest,
    isPrivate: true,
  },
  {
    path: '/purchase/purchase-request/view/:prId',
    exact: true,
    component: ViewPurchaseRequest,
    isPrivate: true,
  },
  {
    path: '/purchase/purchase-request/view/:prId/:tenantId',
    exact: true,
    component: ViewPurchaseRequest,
    isPrivate: true,
  },
  {
    path: '/purchase/purchase-request/multi-vendor/:prId',
    exact: true,
    component: POSellerSelectionForm,
    isPrivate: true,
  },
  {
    path: '/purchase/purchase-request/update/:prId',
    exact: true,
    component: CreatePurchaseRequest,
    isPrivate: true,
  },

  {
    path: '/purchase/purchase-indent',
    exact: true,
    component: PurchaseIndentList,
    isPrivate: true,
  },
  {
    path: '/purchase/purchase-indent/create',
    exact: true,
    component: CreatePurchaseIndent,
    isPrivate: true,
  },
  {
    path: '/purchase/purchase-indent/view/:piId',
    exact: true,
    component: ViewPurchaseIndent,
    isPrivate: true,
  },
  {
    path: '/purchase/purchase-indent/view/:piId/:tenantId',
    exact: true,
    component: ViewPurchaseIndent,
    isPrivate: true,
  },
  {
    path: '/purchase/purchase-indent/update/:piId',
    exact: true,
    component: UpdatePurchaseIndent,
    isPrivate: true,
  },

  // Settings

  {
    path: '/settings/team',
    exact: true,
    component: ManageTeam,
    isPrivate: true,
  },
  {
    path: '/settings/workflow',
    exact: true,
    component: Workflow,
    isPrivate: true,
  },
  {
    path: '/settings/access-control',
    exact: true,
    component: AccessControl,
    isPrivate: true,
  },
  {
    path: '/settings/business-profile',
    exact: true,
    component: MyProfile,
    isPrivate: true,
  },
  {
    path: '/settings/warehouse-setup',
    exact: true,
    component: WarehouseSetup,
    isPrivate: true,
  },
  {
    path: '/purchase/vendor-payouts',
    exact: true,
    component: OutgoingPaymentsList,
    isPrivate: true,
  },
  {
    path: '/payments/create',
    exact: true,
    component: CreateOutgoingPayment,
    isPrivate: true,
  },
  {
    path: '/sales/create-incoming-payments',
    exact: true,
    component: CreateIncomingPayment,
    isPrivate: true,
  },

  // Reports
  {
    path: '/analytics/reports/:reportType',
    exact: true,
    component: ViewReportsPage,
    isPrivate: true,
  },

  {
    path: '/analytics/reports',
    exact: true,
    component: ReportsList,
    isPrivate: true,
  },
  // reports v2
  {
    path: '/analytics/newReports/v2',
    exact: true,
    component: ReportsListV2,
    isPrivate: true,
  },
  {
    path: '/analytics/bulkUpload',
    exact: true,
    component: BulkUpload,
    isPrivate: true,
  },

  // Forecasting
  {
    path: '/analytics/forecasting',
    exact: true,
    component: ForecastsList,
    isPrivate: true,
  },
  {
    path: '/analytics/forecasting/create',
    exact: true,
    component: ForecastForm,
    isPrivate: true,
  },
  {
    path: '/analytics/forecasting/create/:forecastId',
    exact: true,
    component: ForecastForm,
    isPrivate: true,
  },
  // Single Window For Integrations
  {
    path: '/integration/tally',
    exact: true,
    component: TallyIntegration,
    isPrivate: true,
  },
  {
    path: '/integration/busy',
    exact: true,
    component: ReportsListV2,
    isPrivate: true,
  },
  // Production -> Manufacturing Orders
  {
    path: '/production/manufacturing-orders',
    exact: true,
    component: ListManufacturingOrders,
    isPrivate: true,
  },
  {
    path: '/production/manufacturing-orders/create',
    exact: true,
    component: CreateManufacturingOrder,
    isPrivate: true,
  },
  {
    path: '/production/manufacturing-orders/view/:moId',
    exact: true,
    component: ViewManufacturingOrder,
    isPrivate: true,
  },
  {
    path: '/production/manufacturing-orders/update/:moId',
    exact: true,
    component: UpdateManufacturingOrder,
    isPrivate: true,
  },
  // Production -> MRP V2
  {
    path: '/production/planning/v2',
    exact: true,
    component: MrpListV2,
    isPrivate: true,
  },
  {
    path: '/production/planning/v2/create',
    exact: true,
    component: ViewMrpV2,
    isPrivate: true,
  },
  {
    path: '/production/planning/v2/view/:mrpId',
    exact: true,
    component: ViewMrpV2,
    isPrivate: true,
  },
  // Production -> Configurations
  {
    path: '/production/configurations',
    exact: true,
    component: Configurations,
    isPrivate: true,
  },
  {
    path: '/production/job-works',
    exact: true,
    component: JobWorks,
    isPrivate: true,
  },
  {
    path: '/production/bom',
    exact: true,
    component: BOM,
    isPrivate: true,
  },
  {
    path: '/production/bom/create',
    exact: true,
    component: CreateBOM,
    isPrivate: true,
  },
  {
    path: '/production/bom/update/:bomId',
    exact: true,
    component: CreateBOM,
    isPrivate: true,
  },
  {
    path: '/production/bom/view/:bomId',
    exact: true,
    component: ViewBOM,
    isPrivate: true,
  },
  // Production --> Manual Production Entry
  {
    path: '/production/manual-entry',
    exact: true,
    component: ManualProductionEntryList,
    isPrivate: true,
  },
  {
    path: '/production/manual-entry/create',
    exact: true,
    component: ManualProductionEntryForm,
    isPrivate: true,
  },
  {
    path: '/production/manual-entry/update/:peId',
    exact: true,
    component: ManualProductionEntryForm,
    isPrivate: true,
  },
  {
    path: '/production/manual-entry/view/:peId',
    exact: true,
    component: ManualProductionEntryView,
    isPrivate: true,
  },

  // Quality --> Quality Control Points
  {
    path: '/quality/quality-control-points',
    exact: true,
    component: QCPointList,
    isPrivate: true,
  },

  // Quality --> Quality Check Points
  {
    path: '/quality/quality-checks',
    exact: true,
    component: ListQualityChecks,
    isPrivate: true,
  },
  {
    path: '/quality/quality-control-points/create',
    exact: true,
    // component: QCPointForm,
    isPrivate: true,
  },

  // Admin Routes
  {
    path: '/admin/business-units',
    exact: true,
    component: BusinessUnitsAdmin,
    isPrivate: true,
  },
  {
    path: '/admin/users',
    exact: true,
    component: MemberAdmin,
    isPrivate: true,
  },
  {
    path: '/admin/inventory',
    exact: true,
    component: InventoryAdmin,
    isPrivate: true,
  },
  {
    path: '/admin/vendors',
    exact: true,
    component: VendorsAdmin,
    isPrivate: true,
  },
  {
    path: '/admin/customers',
    exact: true,
    component: CustomersAdmin,
    isPrivate: true,
  },
  {
    path: '/admin/configuration',
    exact: true,
    component: AdminConfiguration,
    isPrivate: true,
  },

  // Integration

  {
    path: '/admin/integration',
    exact: true,
    component: IntegrationPage,
    isPrivate: true,
  },

  // Zoho Integration
  {
    path: '/admin/integration/zoho',
    exact: true,
    component: ZohoHome,
    isPrivate: true,
  },
  {
    path: '/admin/integration/zoho/setup',
    exact: true,
    component: ZohoSetup,
    isPrivate: true,
  },

  // Tally Integration
  {
    path: '/admin/integration/tally',
    exact: true,
    component: TallyHome,
    isPrivate: true,
  },
  {
    path: '/admin/integration/tally/setup',
    exact: true,
    component: TallySetup,
    isPrivate: true,
  },

  // Busy Integration
  {
    path: '/admin/integration/busy',
    exact: true,
    component: BusyHome,
    isPrivate: true,
  },
  {
    path: '/admin/integration/busy/setup',
    exact: true,
    component: BusySetup,
    isPrivate: true,
  },

  // Unicommerce Integration
  {
    path: '/admin/integration/unicommerce',
    exact: true,
    component: UnicommerceHome,
    isPrivate: true,
  },
  {
    path: '/admin/integration/unicommerce/setup',
    exact: true,
    component: UnicommerceSetup,
    isPrivate: true,
  },

  // Shopify Integration
  {
    path: '/admin/integration/shopify',
    exact: true,
    component: ShopifyHome,
    isPrivate: true,
  },
  {
    path: '/admin/integration/shopify/setup',
    exact: true,
    component: ShopifySetup,
    isPrivate: true,
  },
  {
    path: '/admin/integration/shopify/connect',
    exact: true,
    component: ShopifyConnect,
    isPrivate: true,
  },
  {
    path: '/helpdesk',
    exact: true,
    component: HelpDesk,
    isPrivate: true,
  },

  // Whatsapp Integration
  {
    path: '/admin/integration/whatsapp',
    exact: true,
    component: WhatsappHome,
    isPrivate: true,
  },
  {
    path: '/admin/integration/whatsapp/setup',
    exact: true,
    component: WhatsappSetup,
    isPrivate: true,
  },
  {
    path: '/admin/integration/whatsapp/connect',
    exact: true,
    component: ShopifyConnect,
    isPrivate: true,
  },

  // Gate Document
  {
    path: '/logistic/gate-document',
    exact: true,
    component: ListGateDocument,
    isPrivate: true,
  },
  {
    path: '/logistic/gate-document/create',
    exact: true,
    component: CreateGateDocument,
    isPrivate: true,
  },
  {
    path: '/logistic/gate-document/view/:gatePassId',
    exact: true,
    component: ViewGateDocument,
    isPrivate: true,
  },
  // Account Payable Invoice
  {
    path: '/purchase/account-payable-invoice',
    exact: true,
    component: AccountPayableInvoiceList,
    isPrivate: true,
  },
  {
    path: '/purchase/account-payable-invoice/create',
    exact: true,
    component: AccountPayableInvoiceForm,
    isPrivate: true,
  },
  {
    path: '/purchase/account-payable-invoice/update/:apiId',
    exact: true,
    component: AccountPayableInvoiceForm,
    isPrivate: true,
  },
  {
    path: '/purchase/account-payable-invoice/view/:apiId',
    exact: true,
    component: AccountPayableInvoiceView,
    isPrivate: true,
  },
  // Purchase Indent -> Multi Vendor PO
  {
    path: '/purchase/purchase-indent/multi-vendor/:piId',
    exact: true,
    component: POSellerSelectionForm,
    isPrivate: true,
  },
  {
    path: '*',
    exact: false,
    component: NotFound,
    isPrivate: false,
  },
];

export default Routes;
