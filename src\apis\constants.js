/* eslint-disable new-cap */
import dayjs from "dayjs";
import Analytics from "analytics";
import segmentPlugin from "@analytics/segment";
import { io } from "socket.io-client";
import { getPageName } from "./getPageName";
import utc from 'dayjs/plugin/utc';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';

dayjs.extend(utc);
dayjs.extend(quarterOfYear);

export const S3_URL = "https://h3-assets.s3.ap-south-1.amazonaws.com";
export const S3_FILE_URL =
  "https://h3-upload-files.s3.ap-south-1.amazonaws.com";

export const H3_TITLE = "Procuzy - Inventory and Manufacturing Software";

export const ISTDateFormat = (date, format) =>
  new dayjs(date).add(5.5, "hours").format(format);
export const toISTDate = (date) => new dayjs(date).add(5.5, "hours");
export const INFINITE_EXPIRY_DATE = "2099-01-01";
export const DEFAULT_CUR_ROUND_OFF =
  Number(localStorage.getItem("price_precision")) || 3;

global.isBlankString = (str) =>
  /^(\s*<p>\s*<br>\s*<\/p>\s*|\s*<p>\s*<\/p>\s*)$/i.test(str);

const minValue = (precision) => {
  if (precision === 1) {
    return 10;
  }
  if (precision === 2) {
    return 100;
  }
  if (precision === 3) {
    return 1000;
  }
  if (precision === 4) {
    return 10000;
  }
  if (precision === 5) {
    return 100000;
  }
  if (precision === 6) {
    return 1000000;
  }
  if (precision === 7) {
    return 10000000;
  }
  return 100;
};

export const QUANTITY = (input, precision) =>
  Math.floor((Number(input) + Number.EPSILON) * minValue(precision)) /
  minValue(precision);
export const QUANTITYROUND = (input, precision) =>
  Math.round((Number(input) + Number.EPSILON) * minValue(precision)) /
  minValue(precision);
export const isEqualWithTolerance = (value1, value2, precision) => {
  const tolerance = Math.pow(10, -precision);
  return Math.abs(value1 - value2) <= tolerance;
};

export const stage = require("../../config.json").environment;

const devAnalytics = {
  name: "dev",
  track: () => console.log("track view fired"),
  page: () => console.log("page view fired"),
  identify: () => console.log("page view identify"),
};

export const analytics =
  stage === "prod"
    ? Analytics({
      app: "h3mart",
      plugins: [
        segmentPlugin({
          writeKey: "zgHIf4sqnvqrKBJqrJh5y3nE3DUH4qYb",
        }),
      ],
    })
    : devAnalytics;

export const imageFormat = ["jpeg", "jpg", "png"];

const licenseKeys = {
  products: {
    dev: "5M6LBYNA05CEJ9BivXmt4yU6HGYTBY",
    uat: "JcyrwyKo3deLJxpHFzqAEtmvSEM4dI",
    prod: "mlm3to4IwYcN74HdoHipNe2rEThUGD",
  },
  vendors: {
    dev: "uRpW1d5E4PmH3fmwbYTE0c7TYvo1CJ",
    uat: "tETF0SmWqMOXuhqXUMnrcXePR1hFyD",
    prod: "7kwP8ga5Wuv07oWuq67N3L1d1vY6Zo",
  },
  customers: {
    dev: "EALoQL2k666ZIyfH5MCaPmuKeexHkj",
    uat: "EpBUvGtAeFCrk996XLvZfQywKfY0wl",
    prod: "31cokhBtZfppK5toewRm75XIo2rMX1",
  },
};

export const getLicenseKey = (type) => licenseKeys?.[type]?.[stage];

export const getRequestUrlAZURE = (stg) => {
  if (localStorage.getItem("override_url")) {
    return localStorage.getItem("override_url");
  }
  if (stg === "dev") return "https://test-dev.procuzy.com";
  if (stg === "uat") return "https://uat.listener.procuzy.com";
  if (stg === "prod") return "https://listener.procuzy.com";
  return "https://test-dev.procuzy.com";
};
export const socket = io.connect(getRequestUrlAZURE(stage));

const getForecastUrlAZURE = (stg) => {
  if (stg === "dev") return "import H3Image from '@Uilib/h3Image";
  if (stg === "uat") return "import H3Image from '@Uilib/h3Image";
  if (stg === "prod") return "import H3Image from '@Uilib/h3Image";
  return "import H3Image from '@Uilib/h3Image";
};

const getReportsUrl = (stg) => {
  if (localStorage.getItem("override_url")) {
    return localStorage.getItem("override_url");
  }

  if (stg === "dev") return "https://test-dev.procuzy.com";
  if (stg === "uat") return "https://uat.listener.procuzy.com";
  if (stg === "prod") return "https://listener.procuzy.com";
  return "https://test-dev.procuzy.com";
};

export const SERVICE_ENDPOINT = {
  GET_KEYBOARD_SHORTCUTS: {
    url: `${getRequestUrlAZURE(stage)}/configuration/keyboard_shortcut`,
    method: 'get',
  },
  SAVE_KEYBOARD_SHORTCUTS: {
    url: `${getRequestUrlAZURE(stage)}/configuration/keyboard_shortcut`,
    method: 'post',
  },
  GET_GLOBAL_SEARCH: {
    url: `${getRequestUrlAZURE(stage)}/global_search`,
    method: 'get',
  },
  // Purchase Order
  UPLOAD_BULK_PO: {
    url: `${getRequestUrlAZURE(stage)}/purchase_order/bulk-upload`,
    method: 'filePost',
  },
  // Purchase Order
  CREATE_SO_FROM_PO: {
    url: `${getRequestUrlAZURE(stage)}/purchase_order/create_so_from_po`,
    method: 'post',
  },
  ADD_BP_MO: {
    url: `${getRequestUrlAZURE(stage)}/manufacturing_order/add_adhoc_bp`,
    method: 'put',
  },
  REMOVE_BP_MO: {
    url: `${getRequestUrlAZURE(stage)}/manufacturing_order/remove_bp`,
    method: 'put',
  },
  GET_SHOPIFY_ERRORS: {
    url: `${getRequestUrlAZURE(stage)}/integration/shopify/order/errors`,
    method: 'get',
  },
  GET_FAVORITE_ENTITY: {
    url: `${getRequestUrlAZURE(stage)}/user/favorite_entity`,
    method: 'get',
  },
  ADD_FAVORITE_ENTITY: {
    url: `${getRequestUrlAZURE(stage)}/user/favorite_entity`,
    method: 'post',
  },
  REMOVE_FAVORITE_ENTITY: {
    url: `${getRequestUrlAZURE(stage)}/user/favorite_entity`,
    method: 'delete',
  },
  WORKFLOWS: {
    url: `${getRequestUrlAZURE(stage)}/template_workflow`,
    method: 'get',
  },
  GET_TALLY_CONFIGURATIONS: {
    url: `${getRequestUrlAZURE(stage)}/integration/tally/configurations`,
    method: 'get',
  },
  GET_PURCHASE_ORDERS: {
    url: `${getRequestUrlAZURE(stage)}/purchase_order`,
    method: 'get',
  },
  SPLIT_STATUS_PO: {
    url: `${getRequestUrlAZURE(stage)}/purchase_order/po-line-status`,
    method: 'put',
  },
  GET_DOCUMENT_SEQUENCE: {
    url: `${getRequestUrlAZURE(stage)}/tenant/document-number-format`,
    method: 'get',
  },
  CREATE_DOCUMENT_SEQUENCE: {
    url: `${getRequestUrlAZURE(stage)}/tenant/document-number-format`,
    method: 'post',
  },
  UPDATE_DOCUMENT_SEQUENCE: {
    url: `${getRequestUrlAZURE(stage)}/tenant/document-number-format`,
    method: 'put',
  },
  MARK_AS_DEFAULT: {
    url: `${getRequestUrlAZURE(stage)}/tenant/document-number-format/set-default`,
    method: 'put',
  },
  GET_BATCHES_BY_MO_ID: {
    url: `${getRequestUrlAZURE(stage)}/manufacturing_order/mo_rm_batches`,
    method: 'get',
  },
  SAVED_FILTER_VIEW: ({ tenantId }) => ({
    url: `${getRequestUrlAZURE(stage)}/tenant/${tenantId}/saved-filters`,
    method: 'get',
  }),
  SAVED_FILTER_VIEW_BY_ID: ({ tenantId, filterId }) => ({
    url: `${getRequestUrlAZURE(stage)}/tenant/${tenantId}/saved-filters/${filterId}`,
    method: 'get',
  }),
  SAVED_FILTER_VIEW_SET_DEFAULT: ({ tenantId, filterId }) => ({
    url: `${getRequestUrlAZURE(stage)}/tenant/${tenantId}/saved-filters/set-default/${filterId}`,
    method: 'put',
  }),
  SAVED_FILTER_VIEW_CLEAR_DEFAULT: ({ tenantId, filterId }) => ({
    url: `${getRequestUrlAZURE(stage)}/tenant/${tenantId}/saved-filters/clear-default/${filterId}`,
    method: 'put',
  }),

  // PURCHASE INDENT API
  CREATE_PURCHASE_INDENT: {
    url: `${getRequestUrlAZURE(stage)}/purchase-indent`,
    method: 'post',
  },
  UPDATE_PURCHASE_INDENT: {
    url: `${getRequestUrlAZURE(stage)}/purchase-indent`,
    method: 'put',
  },
  GET_PURCHASE_INDENT: ({ pi_id }) => ({
    url: `${getRequestUrlAZURE(stage)}/purchase-indent/${pi_id}`,
    method: 'get',
  }),
  GET_PURCHASE_INDENTS: {
    url: `${getRequestUrlAZURE(stage)}/purchase-indent/pi_ids`,
    method: 'get',
  },
  GET_PURCHASE_INDENT_LIST: {
    url: `${getRequestUrlAZURE(stage)}/purchase-indent/list`,
    method: 'get',
  },
  UPDATE_PURCHASE_INDENT_STATUS: {
    url: `${getRequestUrlAZURE(stage)}/purchase-indent/status`,
    method: 'put',
  },
  PURCHASE_INDENT_APPROVAL_WORKFLOW: {
    url: `${getRequestUrlAZURE(stage)}/purchase-indent/workflow`,
    method: 'post',
  },
  GET_ACCOUNT_PAYABLE_INVOICE_WORKFLOWS: {
    url: `${getRequestUrlAZURE(stage)}/account-payable-invoice/workflow`,
    method: 'post',
  },
  ACCOUNT_PAYABLE_INVOICE_APPROVAL_WORKFLOW: {
    url: `${getRequestUrlAZURE(stage)}/account-payable-invoice/workflow`,
    method: 'post',
  },
  UPDATE_ACCOUNT_PAYABLE_INVOICE_STATUS: ({ api_id }) => ({
    url: `${getRequestUrlAZURE(stage)}/account-payable-invoice/status/${api_id}`,
    method: 'put',
  }),
  CREATE_COST_ALLOCATION: {
    url: `${getRequestUrlAZURE(stage)}/landing_cost/link`,
    method: 'post',
  },
  DELETE_COST_ALLOCATION: {
    url: `${getRequestUrlAZURE(stage)}/landing_cost/unlink`,
    method: 'post',
  },

  // expense
  EXPENSE: {
    url: `${getRequestUrlAZURE(stage)}/expense`,
    method: 'get',
  },

  // Account Payable Invoice
  ACCOUNT_PAYABLE_INVOICE: {
    url: `${getRequestUrlAZURE(stage)}/account-payable-invoice/`,
    method: 'post',
  },
  GET_ACCOUNT_PAYABLE_INVOICE_LIST: {
    url: `${getRequestUrlAZURE(stage)}/account-payable-invoice/list`,
    method: 'get',
  },
  GET_ACCOUNT_PAYABLE_INVOICE: ({ apiId }) => ({
    url: `${getRequestUrlAZURE(stage)}/account-payable-invoice/${apiId}`,
    method: 'get',
  }),

  // grn
  GET_GRN: {
    url: `${getRequestUrlAZURE(stage)}/grn`,
    method: 'get',
  },

  // DELIVERY CHALLAN
  DOWNLOAD_DC_LIST: {
    url: `${getRequestUrlAZURE(stage)}/dc/download_list`,
    method: 'post',
  },

  GET_DEVREV_ACCESS_TOKEN: {
    url: `${getRequestUrlAZURE(stage)}/user/devrev_access_token`,
    method: 'get',
  },

  // ----------- Production -------------------------
  MACHINE: {
    url: `${getRequestUrlAZURE(stage)}/routing_process_v2/production_resource`,
    method: 'get',
  },
  SHIFT: {
    url: `${getRequestUrlAZURE(stage)}/routing_process_v2/shift`,
    method: 'get',
  },
  WORK_CENTER: {
    url: `${getRequestUrlAZURE(stage)}/routing_process_v2/production_resource/group`,
    method: 'get',
  },
  OPERATOR: {
    url: `${getRequestUrlAZURE(stage)}/routing_process_v2/operator`,
    method: 'get',
  },
};

export default {
  BASE_URL: `${getRequestUrlAZURE(stage)}`,
  // helper routes
  UPLOAD_FILE: `${getRequestUrlAZURE(stage)}/file_manager/upload`,
  UPLOAD_IMAGE: `${getRequestUrlAZURE(stage)}/file_manager/upload`,
  CDN_URL: 'https://procuzystorage.procuzy.com/',

  // authentication
  REQUEST_OTP: `${getRequestUrlAZURE(stage)}/auth/sendOtp`,
  VERIFY_OTP: `${getRequestUrlAZURE(stage)}/auth/verifyUser`,
  RESEND_OTP: `${getRequestUrlAZURE(stage)}/auth/resendOtp`,
  TENANT_USERS: `${getRequestUrlAZURE(stage)}/tenant/user`,
  ORG_USERS: `${getRequestUrlAZURE(stage)}/tenant/distinct_users`,
  TENANT_ROLES: `${getRequestUrlAZURE(stage)}/tenant/tenant_role`,
  GET_USER_PROFILE: `${getRequestUrlAZURE(stage)}/auth/profile?cached=false`,
  REFRESH_ACCESS_TOKEN: `${getRequestUrlAZURE(stage)}/auth/refreshAccessToken`,
  MY_USER: `${getRequestUrlAZURE(stage)}/user`,
  ORGANISATION: `${getRequestUrlAZURE(stage)}/organisation`,
  PENDING_ACTIONS: `${getRequestUrlAZURE(
    stage
  )}/analytics/user_pending_actions`,
  // product routes
  TENANT_SKUS: `${getRequestUrlAZURE(stage)}/sku`,
  GET_TENANT_SKUS: `${getRequestUrlAZURE(stage)}/sku/v2`,
  GET_SKUS_CODES: `${getRequestUrlAZURE(stage)}/sku/sku_code`,
  GET_SELLER_CODES: `${getRequestUrlAZURE(stage)}/seller/slr-code`,
  GET_SELLER_PRODUCTS: `${getRequestUrlAZURE(stage)}/seller/products`,
  METRICS: `${getRequestUrlAZURE(stage)}/metrics`,
  DOWNLOAD_PRODUCT_BATCHES_LIST: `${getReportsUrl(stage)}/batch/download_list`,
  ARCHIVE_PRODUCTS: `${getRequestUrlAZURE(stage)}/sku/archive`,
  UNARCHIVE_PRODUCTS: `${getRequestUrlAZURE(stage)}/sku/unarchive`,
  SKU_LIST: `${getRequestUrlAZURE(stage)}/sku_list`,

  // tenant routes
  GET_ORG: `${getRequestUrlAZURE(stage)}/auth/organisation/getOrganisation`,
  GET_TENANTS: `${getRequestUrlAZURE(stage)}/tenant`,
  CREATE_TENANT: `${getRequestUrlAZURE(stage)}/tenant`,
  UPDATE_TENANT: `${getRequestUrlAZURE(stage)}/tenant`,
  TENANT_CONFIGURATION: `${getRequestUrlAZURE(stage)}/configuration/tenant`,

  // permissions
  UPDATE_PERMISSION: `${getRequestUrlAZURE(stage)}/tenant/tenant_role`,
  CREATE_PERMISSION: `${getRequestUrlAZURE(stage)}/tenant/tenant_role`,
  GET_PERMISSIONS: `${getRequestUrlAZURE(stage)}/permission`,
  INIT_PERMISSION: `${getRequestUrlAZURE(stage)}/permission/initialize`,

  // seller routes
  SELLERS: `${getRequestUrlAZURE(stage)}/seller`,
  SELLERS_REPORT_DOWNLOAD: `${getRequestUrlAZURE(stage)}/seller/download`,
  SEND_ACCOUNT_STATEMENT_MAIL: `${getRequestUrlAZURE(
    stage
  )}/seller/send-ledger`,
  ARCHIVE_SELLERS: `${getRequestUrlAZURE(stage)}/seller/archive`,
  UNARCHIVE_SELLERS: `${getRequestUrlAZURE(stage)}/seller/unarchive`,
  BULK_SELLER_UPLOAD_SHEET: `${getRequestUrlAZURE(
    stage
  )}/seller/bulk-upload/template/download`,
  BULK_SELLER_UPDATE_SHEET: `${getRequestUrlAZURE(
    stage
  )}/seller/bulk-update/template/download`,
  GET_TALLY_SELLERS: `${getRequestUrlAZURE(stage)}/seller/tally_sellers`,
  LEDGER_ENTRY: `${getRequestUrlAZURE(stage)}/seller/create_ledger`,

  // customer routes
  CUSTOMERS: `${getRequestUrlAZURE(stage)}/customer`,
  ARCHIVE_CUSTOMERS: `${getRequestUrlAZURE(stage)}/customer/archive`,
  UNARCHIVE_CUSTOMERS: `${getRequestUrlAZURE(stage)}/customer/unarchive`,
  GET_ORG_CUSTOMERS: `${getRequestUrlAZURE(stage)}/customer/org_customers`,
  BULK_CUSTOMER_UPLOAD_SHEET: `${getRequestUrlAZURE(
    stage
  )}/customer/bulk-upload/template/download`,
  BULK_CUSTOMER_UPDATE_SHEET: `${getRequestUrlAZURE(
    stage
  )}/customer/bulk-update/template/download`,
  GET_TALLY_CUSTOMERS: `${getRequestUrlAZURE(stage)}/customer/tally_customers`,

  // offers
  OFFERS: `${getRequestUrlAZURE(stage)}/offer`,
  SET_DEFAULT_OFFER: `${getRequestUrlAZURE(stage)}/offer/default`,
  SELLER_OFFERS: `${getRequestUrlAZURE(stage)}/offer/seller_offers`,

  // purchase orders
  PURCHASE_ORDERS: `${getRequestUrlAZURE(stage)}/purchase_order`,
  GET_PO_SEQUENCE: `${getRequestUrlAZURE(stage)}/document_number_format`,
  UPDATE_PO_STATUS: `${getRequestUrlAZURE(stage)}/purchase_order/status`,
  UPDATE_PO_EXPIRY_DATE: `${getRequestUrlAZURE(
    stage
  )}/purchase_order/expiry_date`,
  UPDATE_PO_WORKFLOW_STEP: `${getRequestUrlAZURE(
    stage
  )}/purchase_order/workflow`,
  PURCHASE_ORDERS_DOWNLOAD_DATA: `${getReportsUrl(
    stage
  )}/purchase_order/download_list`,

  // bulk purchase orders
  BULK_PO: `${getRequestUrlAZURE(stage)}/blanket_po`,
  UPDATE_BULK_PO_STATUS: `${getRequestUrlAZURE(stage)}/blanket_po/status`,
  BULK_PO_UPLOAD_SHEET: `${getRequestUrlAZURE(stage)}/purchase_order/bulk-upload/template/download`,

  // purchase requests
  PURCHASE_REQUESTS: `${getRequestUrlAZURE(stage)}/purchase_requisition`,
  PURCHASE_REQUESTS_DOWNLOAD_DATA: `${getReportsUrl(
    stage
  )}/purchase_requisition/download_list`,

  // PURCHASE INDENT
  PURCHASE_INDENT: `${getRequestUrlAZURE(stage)}/purchase-indent`,

  // RFQ
  RFQ: `${getRequestUrlAZURE(stage)}/rfq`,

  // Gate Document
  GATE_DOCUMENT: `${getRequestUrlAZURE(stage)}/gate-document`,

  // grn
  GRN: `${getRequestUrlAZURE(stage)}/grn`,
  GRN_STATUS: `${getRequestUrlAZURE(stage)}/grn/status`,
  GRN_QUICK_UPDATE: `${getRequestUrlAZURE(stage)}/grn/quick`,
  GRN_DOWNLOAD_DATA: `${getReportsUrl(stage)}/grn/download_list`,
  // AP Invoice
  ACCOUNT_PAYABLE_INVOICE: `${getRequestUrlAZURE(stage)}/account-payable-invoice`,


  // address
  ADDRESS: `${getRequestUrlAZURE(stage)}/address`,

  // activity log
  ACTIVITY_LOG: `${getRequestUrlAZURE(stage)}/log/activity_logs`,
  UPDATE_ACTIVITY_LOG_MESSAGE: `${getRequestUrlAZURE(stage)}/log/update_log`,
  GET_HISTORY_LOG_MESSAGE: `${getRequestUrlAZURE(stage)}/log/history_log`,

  // analytics
  ANALYTICS: `${getRequestUrlAZURE(stage)}/analytics`,
  FORECASTING: `${getForecastUrlAZURE(stage)}`,
  BULK_UPLOADS: `${getRequestUrlAZURE(stage)}/background_tasks`,

  // workflows
  WORKFLOWS: `${getRequestUrlAZURE(stage)}/template_workflow`,
  GET_PURCHASE_WORKFLOW_SAMPLE: `${getRequestUrlAZURE(
    stage
  )}/template_workflow/sample`,
  CREATE_WORKFLOWS: `${getRequestUrlAZURE(stage)}/template_workflow/sheet`,

  // payment
  PAYMENT_OUTGOING: `${getRequestUrlAZURE(stage)}/payment_outgoing`,
  PAYMENT_OUTGOING_STATUS: `${getRequestUrlAZURE(
    stage
  )}/payment_outgoing/status`,
  CREATE_AND_LINK_PAYMENT: `${getRequestUrlAZURE(
    stage
  )}/grn/payment/createandlink`,
  LINK_PAYMENT: `${getRequestUrlAZURE(stage)}/grn/payment/`,
  UNLINK_PAYMENT: `${getRequestUrlAZURE(stage)}/grn/payment/`,
  UPDATE_PAYMENT_OUTGOING_STATUS: `${getRequestUrlAZURE(
    stage
  )}/payment_outgoing/status`,
  UPDATE_PAYMENT_OUTGOING_WORKFLOW: `${getRequestUrlAZURE(
    stage
  )}/payment_outgoing/workflow`,
  UNLINK_PO_ADVANCE_PAYMENT: `${getRequestUrlAZURE(
    stage
  )}/purchase_order/unlink_payment`,

  // payment
  PAYMENT_REQUEST: `${getRequestUrlAZURE(stage)}/payment_request`,
  CREATE_PAYMENT_SESSION: `${getRequestUrlAZURE(stage)}/pice/session/payment`,
  CREATE_CONNECT_SESSION: `${getRequestUrlAZURE(stage)}/pice/session/connect`,
  PAYMENT_VERIFY: `${getRequestUrlAZURE(stage)}/pice/payment/verify`,

  // reports
  GET_REPORTS: `${getRequestUrlAZURE(stage)}/report`,
  GET_PRODUCTION_REPORTS: `${getRequestUrlAZURE(stage)}/report/manufacturing`,

  // reports v2
  GET_REPORTS_V2: `${getReportsUrl(stage)}/report`,

  // analytics
  GET_TENANT_ANALYTICS: `${getRequestUrlAZURE(stage)}/analytics`,
  GET_OFFER: `${getRequestUrlAZURE(stage)}/offer`,
  GET_OFFER_BY_SKU: `${getRequestUrlAZURE(stage)}/offer`,
  CART: `${getRequestUrlAZURE(stage)}/cache/tenant/`,

  // adjustment
  CREATE_INDENT: `${getRequestUrlAZURE(stage)}/adjustment`,
  GET_INDENT: `${getRequestUrlAZURE(stage)}/adjustment`,
  UPDATE_INDENT: `${getRequestUrlAZURE(stage)}/adjustment`,
  UPDATE_INDENT_STATUS: `${getRequestUrlAZURE(stage)}/adjustment/status`,

  // uoms
  UOMS: `${getRequestUrlAZURE(stage)}/uomV2`,

  // taxes
  TAXES: `${getRequestUrlAZURE(stage)}/tax/v2`,

  // custom fields
  CF: `${getRequestUrlAZURE(stage)}/custom_fields`,
  CFV2: `${getRequestUrlAZURE(stage)}/custom_fields_v2`,

  // add to cart
  ADD_TO_CART: `${getRequestUrlAZURE(stage)}/add_to_cart`,

  // custom status
  CREATE_CUSTOM_STATUS: `${getRequestUrlAZURE(stage)}/custom_status`,
  UPDATE_CUSTOM_STATUS: `${getRequestUrlAZURE(stage)}/custom_status`,
  GET_CUSTOM_STATUS: `${getRequestUrlAZURE(stage)}/custom_status`,
  DELETE_CUSTOM_STATUS: `${getRequestUrlAZURE(stage)}/custom_status`,
  GET_ORG_STATUS: `${getRequestUrlAZURE(stage)}`,

  // expense
  EXPENSE: `${getRequestUrlAZURE(stage)}/expense`,
  EXPENSE_LINK: `${getRequestUrlAZURE(stage)}/expense/link`,
  EXPENSE_UNLINK: `${getRequestUrlAZURE(stage)}/expense/unlink`,

  // account
  ACCOUNT: `${getRequestUrlAZURE(stage)}/account`,

  // Invoice
  Invoice: `${getRequestUrlAZURE(stage)}/invoice`,
  INVOICE_QUICK_UPDATE: `${getRequestUrlAZURE(stage)}/invoice/quick`,
  Invoice_STATUS: `${getRequestUrlAZURE(stage)}/invoice/status`,
  Invoice_WORKFLOW: `${getRequestUrlAZURE(stage)}/invoice/workflow`,
  INVOICE_LINK_PAYMENT: `${getRequestUrlAZURE(stage)}/invoice/linkpayment`,
  INVOICE_UNLINK_PAYMENT: `${getRequestUrlAZURE(stage)}/invoice/unlinkpayment`,
  DOWNLOAD_SHIPPING_LABELS: `${getRequestUrlAZURE(
    stage
  )}/invoice/shipping_label`,
  DOWNLOAD_INVOICE_LIST: `${getReportsUrl(stage)}/invoice/download_list`,

  // Credit Notes fields
  CN: `${getRequestUrlAZURE(stage)}/credit_note`,
  CN_STATUS: `${getRequestUrlAZURE(stage)}/credit_note/status`,
  CN_WORKFLOW: `${getRequestUrlAZURE(stage)}/credit_note/workflow`,

  // Sales Orders
  SO: `${getRequestUrlAZURE(stage)}/sales_order`,
  SO_STATUS: `${getRequestUrlAZURE(stage)}/sales_order/status`,
  SO_WORKFLOW: `${getRequestUrlAZURE(stage)}/sales_order/workflow`,
  UPDATE_FULFILLMENT_LOCATION: `${getRequestUrlAZURE(
    stage
  )}/integration/shopify/fulfillment/move/location`,
  FULFILL_UF_FO: `${getRequestUrlAZURE(
    stage
  )}/integration/shopify/fulfillment/fulfill`,
  CANCEL_FULFILLMENT: `${getRequestUrlAZURE(
    stage
  )}/integration/shopify/fulfillment/cancel`,
  CREATE_RETURN_FULFILLMENT: `${getRequestUrlAZURE(
    stage
  )}/integration/shopify/order/return`,
  CANCEL_RETURN_FULFILLMENT: `${getRequestUrlAZURE(
    stage
  )}/integration/shopify/order/return/cancel`,
  GET_RETURN_FULFILLMENT: `${getRequestUrlAZURE(
    stage
  )}/integration/shopify/order/returnable_fulfillments`,
  CREATE_REFUND: `${getRequestUrlAZURE(
    stage
  )}/integration/shopify/order/refund`,
  GET_RETURNS: `${getRequestUrlAZURE(stage)}/integration/shopify/order/return`,
  UPDATE_BUNDLE: `${getRequestUrlAZURE(stage)}/sales_order/bundle_line`,
  DELETE_BUNDLE_LINE: `${getRequestUrlAZURE(stage)}/sales_order/bundle_line`,
  DOWNLOAD_SO_LIST: `${getReportsUrl(stage)}/sales_order/download_list`,
  CREATE_BULK_RETURNS: `${getRequestUrlAZURE(
    stage
  )}/integration/shopify/bulk_return`,

  // Debit Notes
  DN: `${getRequestUrlAZURE(stage)}/debit_note`,
  DN_STATUS: `${getRequestUrlAZURE(stage)}/debit_note/status`,
  DN_WORKFLOW: `${getRequestUrlAZURE(stage)}/debit_note/workflow`,

  // Delivery Challan
  DC: `${getRequestUrlAZURE(stage)}/dc`,
  DC_STATUS: `${getRequestUrlAZURE(stage)}/dc/status`,
  DC_QUICK_UPDATE: `${getRequestUrlAZURE(stage)}/dc/quick-update`,

  // Departments
  DEPARTMENTS: `${getRequestUrlAZURE(stage)}/department`,
  DEPARTMENTS_STATUS: `${getRequestUrlAZURE(stage)}/department/status`,

  // Stock Transfers
  ST: `${getRequestUrlAZURE(stage)}/stock_transfer`,
  ST_STATUS: `${getRequestUrlAZURE(stage)}/stock_transfer/status`,
  ST_SHORT_CLOSE: `${getRequestUrlAZURE(stage)}/stock_transfer/short_close`,
  // Manufacturing Orders
  MO: `${getRequestUrlAZURE(stage)}/manufacturing_order`,
  MO_V2: `${getRequestUrlAZURE(stage)}/manufacturing_order_v2`,
  MO_STATUS: `${getRequestUrlAZURE(stage)}/manufacturing_order/status`,
  MO_ADJUSTMENT: `${getRequestUrlAZURE(
    stage
  )}/manufacturing_order/mo_adjustment`,
  MO_ADJUSTMENT_V2: `${getRequestUrlAZURE(
    stage
  )}/manufacturing_order_v2/mo_adjustment_v2`,
  MO_ADJUSTMENT_STATUS: `${getRequestUrlAZURE(
    stage
  )}/manufacturing_order/mo_adjustment/status`,
  MO_EXTRA_CHARGES: `${getRequestUrlAZURE(
    stage
  )}/manufacturing_order/extra_charge`,
  MOV2: `${getRequestUrlAZURE(stage)}/manufacturing_order_v2`,

  // Manual Production Entry
  ManualProductionEntry: `${getRequestUrlAZURE(stage)}/manual_production_entry`,

  // MRP
  MRP: `${getRequestUrlAZURE(stage)}/mrp_run`,
  GET_UNLINKED_POS: `${getRequestUrlAZURE(stage)}/mrp_run/unlinked_pos`,
  LINK_POS: `${getRequestUrlAZURE(stage)}/mrp_run/link`,
  UNLINK_POS: `${getRequestUrlAZURE(stage)}/mrp_run/unlink`,
  // DOWNLOAD_MRP: `${getRequestUrlAZURE(stage)}/mrp_run/download`,

  // MRP V2
  MRP_V2: `${getRequestUrlAZURE(stage)}/mrp/v2`,

  // Resources
  RESOURCE: `${getRequestUrlAZURE(stage)}/routing_process/production_resource`,

  // Process
  PROCESS: `${getRequestUrlAZURE(stage)}/routing_process/production_process`,

  // Template Routes
  TEMPLATE_ROUTE: `${getRequestUrlAZURE(
    stage
  )}/routing_process/template_production_route`,
  TEMPLATE_ROUTE_STATUS: `${getRequestUrlAZURE(
    stage
  )}/routing_process/production_routes/status`,
  UPDATE_ASSIGNEE: `${getRequestUrlAZURE(
    stage
  )}/routing_process/update_assignee`,

  // Job Work
  ROUTING_PROCESS: `${getRequestUrlAZURE(stage)}/routing_process`,
  JOB_WORK_V2: `${getRequestUrlAZURE(stage)}/routing_process_v2/job_work_v2`,
  JOB_WORK_STATUS_V2: `${getRequestUrlAZURE(
    stage
  )}/routing_process_v2/status_v2`,
  JOB_WORK: `${getRequestUrlAZURE(stage)}/routing_process/job_work`,
  UPDATE_JOB_WORK: `${getRequestUrlAZURE(
    stage
  )}/routing_process/production_route_lines`,
  UPDATE_JOB_WORK_V2: `${getRequestUrlAZURE(
    stage
  )}/routing_process_v2/production_route_lines`,
  GET_JOB_OPERATORS: `${getRequestUrlAZURE(stage)}/routing_process/users`,
  PROCESS_JW_MATERIAL: `${getRequestUrlAZURE(stage)}/sku/process_inhouse`,
  DOWNLOAD_JW_V2: `${getRequestUrlAZURE(stage)}/routing_process_v2/job_work_v2/download`,
  // Index
  BOM: `${getRequestUrlAZURE(stage)}/bom`,
  BOM_V2: `${getRequestUrlAZURE(stage)}/bom_v2`,
  BOM_WORKFLOW: `${getRequestUrlAZURE(stage)}/bom_v2/workflow`,
  BOM_V2_JW: `${getRequestUrlAZURE(stage)}/bom_v2/bom_route`,
  BOM_ACTIVATE: `${getRequestUrlAZURE(stage)}/bom/active`,

  // QUALITY
  QC_POINTS: `${getRequestUrlAZURE(stage)}/quality-control-rules`,
  DELETE_QC_POINTS_GROUP: `${getRequestUrlAZURE(
    stage
  )}/quality-control-rules/group`,
  UPDATE_QC_POINT_PRODUCT: `${getRequestUrlAZURE(
    stage
  )}/quality-control-rules/add_product`,
  DOWNLOAD_QC_POINTS: `${getReportsUrl(
    stage
  )}/quality-control-rules/download/qc_rules`,
  QUALITY_CHECK: `${getRequestUrlAZURE(stage)}/quality-checks`,
  QUALITY_CHECK_REPORT: `${getReportsUrl(
    stage
  )}/quality-checks/download/qc_list`,
  QUALITY_CHECK_COA: `${getReportsUrl(stage)}/quality-checks/download/coa`,
  QC_RETEST: `${getRequestUrlAZURE(stage)}/quality-checks/regenerate_qc`,
  QC_ON_HOLD: `${getRequestUrlAZURE(stage)}/quality-checks/update_qc_on_hold`,

  // Categories
  CATEGORIES: `${getRequestUrlAZURE(stage)}/product_category`,
  CREATE_SUB_CATEGORY: `${getRequestUrlAZURE(stage)}/product_category`,

  // Attachment  View
  ATTACHMENT: `${getRequestUrlAZURE(stage)}/attachment`,

  // Inventory Logs
  INVENTORY_LOGS: `${getRequestUrlAZURE(stage)}/inventory_log`,
  DOWNLOAD_INVENTORY_LOGS: `${getReportsUrl(
    stage
  )}/inventory_log/download_logs`,

  // Batch
  BATCH: `${getRequestUrlAZURE(stage)}/sku/batch`,

  // Batch
  GET_BULK_PRODUCT_SHEET: `${getRequestUrlAZURE(stage)}/sku/export/product`,
  GET_BULK_PRODUCT_BATCH_SHEET: `${getRequestUrlAZURE(
    stage
  )}/grn/batch-template`,

  // Vendor Rating
  CREATE_VENDOR_RATING: `${getRequestUrlAZURE(stage)}/rating`,
  UPDATE_VENDOR_RATING_CONFIG: `${getRequestUrlAZURE(stage)}/rating/config`,
  GET_VENDOR_RATING_CONFIG: `${getRequestUrlAZURE(stage)}/rating/config`,
  ENABLE_VENDOR_RATING_CONFIG: `${getRequestUrlAZURE(
    stage
  )}/rating/config/status`,

  // Vendor Price Archive
  ARCHIVE_OFFER_PRICE: `${getRequestUrlAZURE(stage)}/offer/archive`,

  // Configurations
  GENERAL_CONFIGURATION: `${getRequestUrlAZURE(stage)}/admin/entity_settings`,
  LABEL_GENERATION: `${getRequestUrlAZURE(stage)}/label_templates`,
  GLOBAL_LABELS: `${getRequestUrlAZURE(stage)}/label_templates/global_labels`,

  // Currencies
  CURRENCIES: `${getRequestUrlAZURE(stage)}/org_currency`,
  CURRENCY_CONVERSION_RATE: `${getRequestUrlAZURE(
    stage
  )}/currency_conversion_rate`,

  // Extra Charges
  EXTRA_CHARGES: `${getRequestUrlAZURE(stage)}/org_charges`,

  // unlink MO and SO relation
  UNLINK_MO_SO: `${getRequestUrlAZURE(stage)}/manufacturing_order/unlink`,

  // Payment Accounts
  PAYMENT_ACCOUNTS: `${getRequestUrlAZURE(stage)}/payment_accounts`,

  // quick update entityWise
  QUICK_UPDATE_ENTITY_WISE: `${getRequestUrlAZURE(stage)}/quick_updates`,

  // notification centre
  NOTIFICATION_CENTRE: `${getRequestUrlAZURE(
    stage
  )}/notification_center/user_subscriptions`,

  // ----------- Integrations -------------------------

  // Integrations
  INTEGRATIONS: `${getRequestUrlAZURE(stage)}/integration`,

  // e-invoicing
  ENABLE_EWAY_BILL: `${getRequestUrlAZURE(stage)}/integration/e-invoice/enable`,
  AUTH_GST_SUVIDHA: `${getRequestUrlAZURE(
    stage
  )}/integration/e-invoice/authenticate`,
  DEACTIVATE_GST_SUVIDHA: `${getRequestUrlAZURE(
    stage
  )}/integration/e-invoice/deactivate`,
  PUSH_IRP: `${getRequestUrlAZURE(stage)}/integration/e-invoice/push_irp`,
  PUSH_CN_TO_IRP: `${getRequestUrlAZURE(
    stage
  )}/integration/e-invoice/push_irp/credit_note`,
  GEN_EWAY_BILL: `${getRequestUrlAZURE(
    stage
  )}/integration/e-invoice/gen_e_way_bill`,
  GET_EWAY_BILLS: `${getRequestUrlAZURE(
    stage
  )}/integration/e-invoice/get_e_way_bill`,
  CANCEL_IRP: `${getRequestUrlAZURE(stage)}/integration/e-invoice/cancel_irn`,
  CANCEL_EWAY_BILL: `${getRequestUrlAZURE(
    stage
  )}/integration/e-invoice/cancel_ewb`,
  ADD_TRANSPORTATION: `${getRequestUrlAZURE(
    stage
  )}/integration/e-invoice/update_ewb_vehicle`,
  EXCLUDE_EWAY_BILLS: `${getRequestUrlAZURE(
    stage
  )}/integration/e-invoice/exclude`,
  FETCH_EWAY_BILL: `${getRequestUrlAZURE(
    stage
  )}/integration/e-invoice/pull_ewb`,
  DOWNLOAD_E_WAY_BILL: `${getRequestUrlAZURE(
    stage
  )}/integration/e-invoice/print_ewb`,
  DOWNLOAD_E_INVOICE: `${getRequestUrlAZURE(
    stage
  )}/integration/e-invoice/print_irn`,
  // Tally Integrations
  CONNECT_TO_TALLY: `${getRequestUrlAZURE(stage)}/integration/tally/connect`,
  REFRESH_TALLY_CONNECTIONS: `${getRequestUrlAZURE(
    stage
  )}/integration/tally/refresh`,
  GET_TALLY_CONNECTIONS: `${getRequestUrlAZURE(stage)}/integration/tally/list`,
  CONNECT_TENANT_TO_TALLY_LOCATION: `${getRequestUrlAZURE(
    stage
  )}/integration/tally/location_map`,
  SYNC_TALLY_PRODUCT: `${getRequestUrlAZURE(
    stage
  )}/integration/tally/product/sync`,
  SYNC_TALLY_VENDOR: `${getRequestUrlAZURE(
    stage
  )}/integration/tally/vendor/sync`,
  SYNC_TALLY_GRN: `${getRequestUrlAZURE(stage)}/integration/tally/bill/sync`,
  SYNC_TALLY_PO: `${getRequestUrlAZURE(
    stage
  )}/integration/tally/purchase_order/sync`,
  DELETE_TALLY_CONNECTION: `${getRequestUrlAZURE(
    stage
  )}/integration/tally/delete`,
  PULL_TALLY_PRODUCTS: `${getRequestUrlAZURE(
    stage
  )}/integration/tally/product/pull`,
  PULL_TALLY_VENDORS: `${getRequestUrlAZURE(
    stage
  )}/integration/tally/vendor/pull`,
  PULL_TALLY_VENDOR_PAYMENT: `${getRequestUrlAZURE(
    stage
  )}/integration/tally/vendor/payment/pull`,
  SYNC_TALLY_INVOICE: `${getRequestUrlAZURE(
    stage
  )}/integration/tally/invoice/sync`,
  SYNC_TALLY_CN: `${getRequestUrlAZURE(
    stage
  )}/integration/tally/credit_note/sync`,
  PULL_TALLY_CUSTOMERS: `${getRequestUrlAZURE(
    stage
  )}/integration/tally/customers/pull`,
  GET_TALLY_DOCUMENTS: `${getRequestUrlAZURE(
    stage
  )}/integration/tally//get_documents`,
  GET_TALLY_UNMAPPED_DOCUMENTS: `${getRequestUrlAZURE(
    stage
  )}/integration/tally/unmapped_documents`,
  UPDATE_TALLY_EXCLUDED_DOCUMENTS: `${getRequestUrlAZURE(
    stage
  )}/integration/tally/exclude_documents`,
  SYNC_TALLY_DOCUMENTS: `${getRequestUrlAZURE(
    stage
  )}/integration/tally/push_documents`,
  TALLY_DOCUMENTS_COUNT: `${getRequestUrlAZURE(
    stage
  )}/integration/tally/documents_count`,

  // Busy Integrations
  BUSY_CONNECTIONS: `${getRequestUrlAZURE(stage)}/integration/busy/connection`,
  IMPORT_BUSY_PRODUCTS: `${getRequestUrlAZURE(
    stage
  )}/integration/busy/product/import`,
  IMPORT_BUSY_VENDORS: `${getRequestUrlAZURE(
    stage
  )}/integration/busy/vendor/import`,
  PUSH_GRN_TO_BUSY: `${getRequestUrlAZURE(stage)}/integration/busy/push_grn`,
  PUSH_INVOICE_TO_BUSY: `${getRequestUrlAZURE(
    stage
  )}/integration/busy/push_invoice`,
  PUSH_EXPENSE_TO_BUSY: `${getRequestUrlAZURE(
    stage
  )}/integration/busy/push_expense`,
  PUSH_CREDIT_NOTE_TO_BUSY: `${getRequestUrlAZURE(
    stage
  )}/integration/busy/push_credit_note`,
  PUSH_DEBIT_NOTE_TO_BUSY: `${getRequestUrlAZURE(
    stage
  )}/integration/busy/push_debit_note`,

  // Zoho Integrations
  CONNECT_TO_ZOHO: `${getRequestUrlAZURE(stage)}/integration/zoho/connect`,
  GET_ZOHO_CONNECTIONS: `${getRequestUrlAZURE(stage)}/integration/zoho/list`,
  CONNECT_TENANT_TO_LOCATION: `${getRequestUrlAZURE(
    stage
  )}/integration/zoho/location_map`,
  SYNC_ZOHO_PRODUCT: `${getRequestUrlAZURE(
    stage
  )}/integration/zoho/product/sync`,
  SYNC_ZOHO_VENDOR: `${getRequestUrlAZURE(stage)}/integration/zoho/vendor/sync`,
  SYNC_ZOHO_GRN: `${getRequestUrlAZURE(stage)}/integration/zoho/bill/sync`,
  SYNC_ZOHO_DN: `${getRequestUrlAZURE(stage)}/integration/zoho/debit_note/sync`,
  SYNC_ZOHO_PO: `${getRequestUrlAZURE(
    stage
  )}/integration/zoho/purchase_order/sync`,
  SYNC_ZOHO_INVOICE: `${getRequestUrlAZURE(
    stage
  )}/integration/zoho/invoice/sync`,
  SYNC_ZOHO_CN: `${getRequestUrlAZURE(
    stage
  )}/integration/zoho/credit_note/sync`,
  UPDATE_ZOHO_CONNECTION: `${getRequestUrlAZURE(stage)}/integration/zoho`,
  DELETE_ZOHO_CONNECTION: `${getRequestUrlAZURE(stage)}/integration/zoho`,
  ZOHO_AUTOMATIC_DOC_SYNC: `${getRequestUrlAZURE(
    stage
  )}/integration/zoho/automatic_document_sync`,

  // UPDATE_ZOHO_CONFIGURATION: `${getRequestUrlAZURE(stage)}/integration/zoho/configuration`,
  PULL_ZOHO_PRODUCTS: `${getRequestUrlAZURE(
    stage
  )}/integration/zoho/workflow/pull_products`,
  PULL_ZOHO_VENDORS: `${getRequestUrlAZURE(
    stage
  )}/integration/zoho/workflow/pull_vendors`,
  PULL_ZOHO_CUSTOMERS: `${getRequestUrlAZURE(
    stage
  )}/integration/zoho/workflow/pull_customers`,

  // Unicommerce Integrations
  CONNECT_TO_UNICOMMERCE: `${getRequestUrlAZURE(
    stage
  )}/integration/unicommerce/connect`,
  GET_UNICOMMERCE_CONNECTIONS: `${getRequestUrlAZURE(
    stage
  )}/integration/unicommerce/get_integration`,
  CONNECT_TENANT_TO_LOCATION_UNICOMMERCE: `${getRequestUrlAZURE(
    stage
  )}/integration/unicommerce/update_tenant_facility`,
  VERIFY_LOCATION: `${getRequestUrlAZURE(
    stage
  )}/integration/unicommerce/verify_facility_code`,
  SYNC_PRODUCT: `${getRequestUrlAZURE(
    stage
  )}/integration/unicommerce/sync_products`,
  SYNC_SO: `${getRequestUrlAZURE(
    stage
  )}/integration/unicommerce/sync_sales_order`,
  ADJUST_UNICOMMERECE_INVENTORY: `${getRequestUrlAZURE(
    stage
  )}/integration/unicommerce/adjust_inventory`,
  UPDATE_UNICOMMERCE_CONFIGURATION: `${getRequestUrlAZURE(
    stage
  )}/integration/unicommerce/update_configuration`,
  SCHEDULE_UNICOMMERCE_SYNC: `${getRequestUrlAZURE(
    stage
  )}/integration/unicommerce/schedule_job`,

  // Shopify Integrations
  CONNECT_TO_SHOPIFY: `${getRequestUrlAZURE(
    stage
  )}/integration/shopify/connect`,
  DELETE_SHOPIFY_CONNECTION: `${getRequestUrlAZURE(
    stage
  )}/integration/shopify/disconnect`,
  GET_SHOPIFY_CONNECTIONS: `${getRequestUrlAZURE(
    stage
  )}/integration/shopify/get_integration`,
  CONNECT_TENANT_TO_SHOPIFY_LOCATION: `${getRequestUrlAZURE(
    stage
  )}/integration/shopify/update_integration`,
  UPDATE_SHOPIFY_CONFIGURATION: `${getRequestUrlAZURE(
    stage
  )}/integration/shopify/configuration`,
  SYNC_SHOPIFY_INVENTORY: `${getRequestUrlAZURE(
    stage
  )}/integration/shopify/inventory/sync`,
  SYNC_SHOPIFY_CUSTOMER: `${getRequestUrlAZURE(
    stage
  )}/integration/shopify/customer/sync`,
  SYNC_SHOPIFY_SO: `${getRequestUrlAZURE(
    stage
  )}/integration/shopify/orders/sync`,
  SYNC_SHOPIFY_ORDERS: `${getRequestUrlAZURE(
    stage
  )}/integration/shopify/pull-orders`,
  CANCEL_SHOPIFY_ORDER: `${getRequestUrlAZURE(
    stage
  )}/integration/shopify/order/cancel`,
  CREATE_ADHOC: `${getRequestUrlAZURE(
    stage
  )}/integration/shopify/order/add_line`,
  SHOPIFY: `${getRequestUrlAZURE(stage)}/integration/shopify`,

  // Whatsapp Integrations
  TOGGLE_WHATSAPP: `${getRequestUrlAZURE(stage)}/integration/whatsapp/toggle`,
  UPDATE_WHATSAPP_CONFIGURATION: `${getRequestUrlAZURE(
    stage
  )}/integration/whatsapp/tenant_integration`,
  GET_WHATSAPP_CONNECTIONS: `${getRequestUrlAZURE(
    stage
  )}/integration/whatsapp/org_integration`,

  // Settings
  INVENTORY_LOCATION: `${getRequestUrlAZURE(stage)}/inventory_location`,
  MASKING_POLICY: `${getRequestUrlAZURE(stage)}/data_masking_policy`,

  // ----------- Integrations -------------------------

  // Payment Terms
  PAYMENT_TERMS: `${getRequestUrlAZURE(stage)}/organisation/payment_terms`,

  // Inventory
  PRODUCT_BUNDLE: `${getRequestUrlAZURE(stage)}/sku/bundle`,
  BULK_SKU_UPLOAD_SHEET: `${getRequestUrlAZURE(
    stage
  )}/sku/bulk-upload/template/download`,
  BULK_SKU_UPDATE_SHEET: `${getRequestUrlAZURE(
    stage
  )}/sku/bulk-update/template/download`,
  GET_TALLY_PRODUCTS: `${getRequestUrlAZURE(stage)}/sku/tally_products`,

  // Price List
  PRICE_LIST: `${getRequestUrlAZURE(stage)}/price_list`,

  // Stock Master
  STOCK_MASTER: `${getRequestUrlAZURE(stage)}/batch/get`,
  STOCK_MASTER_UPDATE: `${getRequestUrlAZURE(stage)}/batch/update`,
  STOCK_MASTER_ARCHIVE: `${getRequestUrlAZURE(stage)}/batch/archive`,
  ADJUSTMENT_BATCH: `${getRequestUrlAZURE(stage)}/batch/update/quantity`,

  // Daily Run Rate
  DAILY_RUN_RATE: `${getRequestUrlAZURE(stage)}/drr`,

  // Custom Messages
  CUSTOM_REASON: `${getRequestUrlAZURE(stage)}/entities/custom_reasons`,

  TAG: `${getRequestUrlAZURE(stage)}/tags`,

  // Members
  MEMBERS: `${getRequestUrlAZURE(stage)}/admin/user_management`,

  // Document Configuration
  DOCUMENT_CONFIGURATION: `${getRequestUrlAZURE(
    stage
  )}/application/document_configuration`,

  TDS_TCS: `${getRequestUrlAZURE(stage)}/tcs_tds_taxes`,

  // Reservation
  RESERVATION: `${getRequestUrlAZURE(stage)}/reserve`,

  // OCR
  OCR: `${getRequestUrlAZURE(stage)}/ocr`,
};
// ----------- segments -------------------------

export const Pages = {
  // Inventory
  product: getPageName("PRODUCT"),
  bom: getPageName("BILL_OF_MATERIAL"),
  sa: getPageName("STOCK_ADJUSTMENT"),
  st: getPageName("STOCK_TRANSFER"),
  dc: getPageName("DELIVERY_CHALLAN"),

  // Purchase
  vendor: getPageName("VENDOR"),
  pr: getPageName("PURCHASE_REQUEST"),
  pi: getPageName('PURCHASE_INDENT'),
  po: getPageName("PURCHASE_ORDER"),
  grn: getPageName("GOOD_RECEIVING_NOTES"),
  api: getPageName("ACCOUNT_PAYABLE_INVOICE"),
  dn: getPageName("DEBIT_NOTE"),
  vp: getPageName("VENDOR_PAYMENT"),
  em: getPageName("EXPENSES"),

  // Production
  mo: getPageName("MANUFACTURING_ORDER"),

  // Quality
  qcp: getPageName("QUALITY_CONTROL_POINT"),
  qc: getPageName("QUALITY_CHECK"),

  // Sales
  customer: getPageName("CUSTOMER"),
  so: getPageName("SALES_ORDER"),
  invoice: getPageName("INVOICE"),
  cn: getPageName("CREDIT_NOTE"),

  // Reports
  reports: getPageName("REPORTS"),
  vps: getPageName("VENDOR_PURCHASE_SUMMARY"),
  pps: getPageName("PRODUCT_PURCHASE_SUMMARY"),
  pl: getPageName("PURCHASE_LEDGER"),
  as: getPageName("ADJUSTMENT_SUMMARY"),
  is: getPageName("INVENTORY_SUMMARY"),

  // Setting
  user: getPageName("USER"),
  role: getPageName("ROLE"),
  department: getPageName("DEPARTMENT"),
  workflow: getPageName("WORKFLOW"),
  companyProfile: getPageName("COMPANY_PROFILE"),
  documentSequence: getPageName("DOCUMENT_SEQUENCE"),
  myProfile: getPageName("MY_PROFILE"),

  // Auth
  login: getPageName("LOGIN"),

  // home
  home: getPageName("HOME"),

  // pending actions
  pendingPr: getPageName("PENDING_PURCHASE_REQUESTS_FOR_APPROVAL"),
  pendingPi: getPageName("PENDING_PURCHASE_INDENTS_FOR_APPROVAL"),
  pendingPo: getPageName("PENDING_PURCHASE_ORDERS_FOR_APPROVAL"),
  pendingSo: getPageName("PENDING_SALES_ORDERS_FOR_APPROVAL"),
  pendingInvoice: getPageName("PENDING_INVOICE_FOR_APPROVAL"),
  pendingPayment: getPageName("PENDING_PAYMENT_FOR_APPROVAL"),

  // Bulk Upload
  bulkUpload: getPageName("BULK_UPLOAD"),
};

export const AdminPages = {
  // Inventory
  product: getPageName("ADMIN_PRODUCT"),
  bu: getPageName("BUSINESS_UNIT"),
  vendor: getPageName("VENDOR"),
  customer: getPageName("CUSTOMER"),
  configuration: getPageName("CONFIGURATION"),
  dc: getPageName("DELIVERY_CHALLAN"),
};

export const IndianStates = [
  { name: "ANDAMAN AND NICOBAR", code: 35 },
  { name: "ANDHRA PRADESH", code: 37 },
  { name: "ARUNACHAL PRADESH", code: 12 },
  { name: "ASSAM", code: 18 },
  { name: "BIHAR", code: 10 },
  { name: "CHANDIGARH", code: 4 },
  { name: "CHHATTISGARH", code: 22 },
  { name: "DADAR AND NAGAR HAVELI", code: 26 },
  { name: "DAMAN AND DIU", code: 25 },
  { name: "DELHI", code: 7 },
  { name: "GOA", code: 30 },
  { name: "GUJARAT", code: 24 },
  { name: "HARYANA", code: 6 },
  { name: "HIMACHAL PRADESH", code: 2 },
  { name: "JAMMU AND KASHMIR", code: 1 },
  { name: "JHARKHAND", code: 20 },
  { name: "KARNATAKA", code: 29 },
  { name: "KERALA", code: 32 },
  { name: "LAKSHADWEEP", code: 31 },
  { name: "MADHYA PRADESH", code: 23 },
  { name: "MAHARASTRA", code: 27 },
  { name: "MANIPUR", code: 14 },
  { name: "MEGHALAYA", code: 17 },
  { name: "MIZORAM", code: 15 },
  { name: "NAGALAND", code: 13 },
  { name: "ORISSA", code: 21 },
  { name: "PUDUCHERRY", code: 34 },
  { name: "PUNJAB", code: 3 },
  { name: "RAJASTHAN", code: 8 },
  { name: "SIKKIM", code: 11 },
  { name: "TAMIL NADU", code: 33 },
  { name: "TELANGANA", code: 36 },
  { name: "TRIPURA", code: 16 },
  { name: "UTTAR PRADESH", code: 9 },
  { name: "UTTARAKHAND", code: 5 },
  { name: "WEST BENGAL", code: 19 },
  { name: "OTHER TERRITORY", code: 97 },
  { name: "OTHER COUNTRY", code: 99 },
];

export const Countries = [
  { name: "Afghanistan", code: "AF" },
  { name: "Åland Islands", code: "AX" },
  { name: "Albania", code: "AL" },
  { name: "Algeria", code: "DZ" },
  { name: "American Samoa", code: "AS" },
  { name: "Andorra", code: "AD" },
  { name: "Angola", code: "AO" },
  { name: "Anguilla", code: "AI" },
  { name: "Antarctica", code: "AQ" },
  { name: "Antigua and Barbuda", code: "AG" },
  { name: "Argentina", code: "AR" },
  { name: "Armenia", code: "AM" },
  { name: "Aruba", code: "AW" },
  { name: "Australia", code: "AU" },
  { name: "Austria", code: "AT" },
  { name: "Azerbaijan", code: "AZ" },
  { name: "Bahamas", code: "BS" },
  { name: "Bahrain", code: "BH" },
  { name: "Bangladesh", code: "BD" },
  { name: "Barbados", code: "BB" },
  { name: "Belarus", code: "BY" },
  { name: "Belgium", code: "BE" },
  { name: "Belize", code: "BZ" },
  { name: "Benin", code: "BJ" },
  { name: "Bermuda", code: "BM" },
  { name: "Bhutan", code: "BT" },
  { name: "Bolivia", code: "BO" },
  { name: "Bosnia and Herzegovina", code: "BA" },
  { name: "Botswana", code: "BW" },
  { name: "Bouvet Island", code: "BV" },
  { name: "Brazil", code: "BR" },
  { name: "British Indian Ocean Territory", code: "IO" },
  { name: "Brunei Darussalam", code: "BN" },
  { name: "Bulgaria", code: "BG" },
  { name: "Burkina Faso", code: "BF" },
  { name: "Burundi", code: "BI" },
  { name: "Cambodia", code: "KH" },
  { name: "Cameroon", code: "CM" },
  { name: "Canada", code: "CA" },
  { name: "Cape Verde", code: "CV" },
  { name: "Cayman Islands", code: "KY" },
  { name: "Central African Republic", code: "CF" },
  { name: "Chad", code: "TD" },
  { name: "Chile", code: "CL" },
  { name: "China", code: "CN" },
  { name: "Christmas Island", code: "CX" },
  { name: "Cocos (Keeling) Islands", code: "CC" },
  { name: "Colombia", code: "CO" },
  { name: "Comoros", code: "KM" },
  { name: "Congo", code: "CG" },
  { name: "Congo, The Democratic Republic of the", code: "CD" },
  { name: "Cook Islands", code: "CK" },
  { name: "Costa Rica", code: "CR" },
  { name: "Cote D'Ivoire", code: "CI" },
  { name: "Croatia", code: "HR" },
  { name: "Cuba", code: "CU" },
  { name: "Cyprus", code: "CY" },
  { name: "Czech Republic", code: "CZ" },
  { name: "Denmark", code: "DK" },
  { name: "Djibouti", code: "DJ" },
  { name: "Dominica", code: "DM" },
  { name: "Dominican Republic", code: "DO" },
  { name: "Ecuador", code: "EC" },
  { name: "Egypt", code: "EG" },
  { name: "El Salvador", code: "SV" },
  { name: "Equatorial Guinea", code: "GQ" },
  { name: "Eritrea", code: "ER" },
  { name: "Estonia", code: "EE" },
  { name: "Ethiopia", code: "ET" },
  { name: "Falkland Islands (Malvinas)", code: "FK" },
  { name: "Faroe Islands", code: "FO" },
  { name: "Fiji", code: "FJ" },
  { name: "Finland", code: "FI" },
  { name: "France", code: "FR" },
  { name: "French Guiana", code: "GF" },
  { name: "French Polynesia", code: "PF" },
  { name: "French Southern Territories", code: "TF" },
  { name: "Gabon", code: "GA" },
  { name: "Gambia", code: "GM" },
  { name: "Georgia", code: "GE" },
  { name: "Germany", code: "DE" },
  { name: "Ghana", code: "GH" },
  { name: "Gibraltar", code: "GI" },
  { name: "Greece", code: "GR" },
  { name: "Greenland", code: "GL" },
  { name: "Grenada", code: "GD" },
  { name: "Guadeloupe", code: "GP" },
  { name: "Guam", code: "GU" },
  { name: "Guatemala", code: "GT" },
  { name: "Guernsey", code: "GG" },
  { name: "Guinea", code: "GN" },
  { name: "Guinea-Bissau", code: "GW" },
  { name: "Guyana", code: "GY" },
  { name: "Haiti", code: "HT" },
  { name: "Heard Island and Mcdonald Islands", code: "HM" },
  { name: "Holy See (Vatican City State)", code: "VA" },
  { name: "Honduras", code: "HN" },
  { name: "Hong Kong", code: "HK" },
  { name: "Hungary", code: "HU" },
  { name: "Iceland", code: "IS" },
  { name: "India", code: "IN" },
  { name: "Indonesia", code: "ID" },
  { name: "Iran, Islamic Republic Of", code: "IR" },
  { name: "Iraq", code: "IQ" },
  { name: "Ireland", code: "IE" },
  { name: "Isle of Man", code: "IM" },
  { name: "Israel", code: "IL" },
  { name: "Italy", code: "IT" },
  { name: "Jamaica", code: "JM" },
  { name: "Japan", code: "JP" },
  { name: "Jersey", code: "JE" },
  { name: "Jordan", code: "JO" },
  { name: "Kazakhstan", code: "KZ" },
  { name: "Kenya", code: "KE" },
  { name: "Kiribati", code: "KI" },
  { name: "Korea, Democratic People'S Republic of", code: "KP" },
  { name: "Korea, Republic of", code: "KR" },
  { name: "Kuwait", code: "KW" },
  { name: "Kyrgyzstan", code: "KG" },
  { name: "Lao People'S Democratic Republic", code: "LA" },
  { name: "Latvia", code: "LV" },
  { name: "Lebanon", code: "LB" },
  { name: "Lesotho", code: "LS" },
  { name: "Liberia", code: "LR" },
  { name: "Libyan Arab Jamahiriya", code: "LY" },
  { name: "Liechtenstein", code: "LI" },
  { name: "Lithuania", code: "LT" },
  { name: "Luxembourg", code: "LU" },
  { name: "Macao", code: "MO" },
  { name: "Macedonia, The Former Yugoslav Republic of", code: "MK" },
  { name: "Madagascar", code: "MG" },
  { name: "Malawi", code: "MW" },
  { name: "Malaysia", code: "MY" },
  { name: "Maldives", code: "MV" },
  { name: "Mali", code: "ML" },
  { name: "Malta", code: "MT" },
  { name: "Marshall Islands", code: "MH" },
  { name: "Martinique", code: "MQ" },
  { name: "Mauritania", code: "MR" },
  { name: "Mauritius", code: "MU" },
  { name: "Mayotte", code: "YT" },
  { name: "Mexico", code: "MX" },
  { name: "Micronesia, Federated States of", code: "FM" },
  { name: "Moldova, Republic of", code: "MD" },
  { name: "Monaco", code: "MC" },
  { name: "Mongolia", code: "MN" },
  { name: "Montserrat", code: "MS" },
  { name: "Morocco", code: "MA" },
  { name: "Mozambique", code: "MZ" },
  { name: "Myanmar", code: "MM" },
  { name: "Namibia", code: "NA" },
  { name: "Nauru", code: "NR" },
  { name: "Nepal", code: "NP" },
  { name: "Netherlands", code: "NL" },
  { name: "Netherlands Antilles", code: "AN" },
  { name: "New Caledonia", code: "NC" },
  { name: "New Zealand", code: "NZ" },
  { name: "Nicaragua", code: "NI" },
  { name: "Niger", code: "NE" },
  { name: "Nigeria", code: "NG" },
  { name: "Niue", code: "NU" },
  { name: "Norfolk Island", code: "NF" },
  { name: "Northern Mariana Islands", code: "MP" },
  { name: "Norway", code: "NO" },
  { name: "Oman", code: "OM" },
  { name: "Pakistan", code: "PK" },
  { name: "Palau", code: "PW" },
  { name: "Palestinian Territory, Occupied", code: "PS" },
  { name: "Panama", code: "PA" },
  { name: "Papua New Guinea", code: "PG" },
  { name: "Paraguay", code: "PY" },
  { name: "Peru", code: "PE" },
  { name: "Philippines", code: "PH" },
  { name: "Pitcairn", code: "PN" },
  { name: "Poland", code: "PL" },
  { name: "Portugal", code: "PT" },
  { name: "Puerto Rico", code: "PR" },
  { name: "Qatar", code: "QA" },
  { name: "Reunion", code: "RE" },
  { name: "Romania", code: "RO" },
  { name: "Russian Federation", code: "RU" },
  { name: "RWANDA", code: "RW" },
  { name: "Saint Helena", code: "SH" },
  { name: "Saint Kitts and Nevis", code: "KN" },
  { name: "Saint Lucia", code: "LC" },
  { name: "Saint Pierre and Miquelon", code: "PM" },
  { name: "Saint Vincent and the Grenadines", code: "VC" },
  { name: "Samoa", code: "WS" },
  { name: "San Marino", code: "SM" },
  { name: "Sao Tome and Principe", code: "ST" },
  { name: "Saudi Arabia", code: "SA" },
  { name: "Senegal", code: "SN" },
  { name: "Serbia and Montenegro", code: "CS" },
  { name: "Seychelles", code: "SC" },
  { name: "Sierra Leone", code: "SL" },
  { name: "Singapore", code: "SG" },
  { name: "Slovakia", code: "SK" },
  { name: "Slovenia", code: "SI" },
  { name: "Solomon Islands", code: "SB" },
  { name: "Somalia", code: "SO" },
  { name: "South Africa", code: "ZA" },
  { name: "South Georgia and the South Sandwich Islands", code: "GS" },
  { name: "Spain", code: "ES" },
  { name: "Sri Lanka", code: "LK" },
  { name: "Sudan", code: "SD" },
  { name: "Suriname", code: "SR" },
  { name: "Svalbard and Jan Mayen", code: "SJ" },
  { name: "Swaziland", code: "SZ" },
  { name: "Sweden", code: "SE" },
  { name: "Switzerland", code: "CH" },
  { name: "Syrian Arab Republic", code: "SY" },
  { name: "Taiwan, Province of China", code: "TW" },
  { name: "Tajikistan", code: "TJ" },
  { name: "Tanzania, United Republic of", code: "TZ" },
  { name: "Thailand", code: "TH" },
  { name: "Timor-Leste", code: "TL" },
  { name: "Togo", code: "TG" },
  { name: "Tokelau", code: "TK" },
  { name: "Tonga", code: "TO" },
  { name: "Trinidad and Tobago", code: "TT" },
  { name: "Tunisia", code: "TN" },
  { name: "Turkey", code: "TR" },
  { name: "Turkmenistan", code: "TM" },
  { name: "Turks and Caicos Islands", code: "TC" },
  { name: "Tuvalu", code: "TV" },
  { name: "Uganda", code: "UG" },
  { name: "Ukraine", code: "UA" },
  { name: "United Arab Emirates", code: "AE" },
  { name: "United Kingdom", code: "GB" },
  { name: "United States", code: "US" },
  { name: "United States Minor Outlying Islands", code: "UM" },
  { name: "Uruguay", code: "UY" },
  { name: "Uzbekistan", code: "UZ" },
  { name: "Vanuatu", code: "VU" },
  { name: "Venezuela", code: "VE" },
  { name: "Viet Nam", code: "VN" },
  { name: "Virgin Islands, British", code: "VG" },
  { name: "Virgin Islands, U.S.", code: "VI" },
  { name: "Wallis and Futuna", code: "WF" },
  { name: "Western Sahara", code: "EH" },
  { name: "Yemen", code: "YE" },
  { name: "Zambia", code: "ZM" },
  { name: "Zimbabwe", code: "ZW" },
];

export const CountryCodes = [
  {
    name: "Afghanistan",
    flag: "🇦🇫",
    code: "AF",
    dial_code: "93",
  },
  {
    name: "Åland Islands",
    flag: "🇦🇽",
    code: "AX",
    dial_code: "358",
  },
  {
    name: "Albania",
    flag: "🇦🇱",
    code: "AL",
    dial_code: "355",
  },
  {
    name: "Algeria",
    flag: "🇩🇿",
    code: "DZ",
    dial_code: "213",
  },
  {
    name: "American Samoa",
    flag: "🇦🇸",
    code: "AS",
    dial_code: "1684",
  },
  {
    name: "Andorra",
    flag: "🇦🇩",
    code: "AD",
    dial_code: "376",
  },
  {
    name: "Angola",
    flag: "🇦🇴",
    code: "AO",
    dial_code: "244",
  },
  {
    name: "Anguilla",
    flag: "🇦🇮",
    code: "AI",
    dial_code: "1264",
  },
  {
    name: "Antarctica",
    flag: "🇦🇶",
    code: "AQ",
    dial_code: "672",
  },
  {
    name: "Antigua and Barbuda",
    flag: "🇦🇬",
    code: "AG",
    dial_code: "1268",
  },
  {
    name: "Argentina",
    flag: "🇦🇷",
    code: "AR",
    dial_code: "54",
  },
  {
    name: "Armenia",
    flag: "🇦🇲",
    code: "AM",
    dial_code: "374",
  },
  {
    name: "Aruba",
    flag: "🇦🇼",
    code: "AW",
    dial_code: "297",
  },
  {
    name: "Australia",
    flag: "🇦🇺",
    code: "AU",
    dial_code: "61",
  },
  {
    name: "Austria",
    flag: "🇦🇹",
    code: "AT",
    dial_code: "43",
  },
  {
    name: "Azerbaijan",
    flag: "🇦🇿",
    code: "AZ",
    dial_code: "994",
  },
  {
    name: "Bahamas",
    flag: "🇧🇸",
    code: "BS",
    dial_code: "1242",
  },
  {
    name: "Bahrain",
    flag: "🇧🇭",
    code: "BH",
    dial_code: "973",
  },
  {
    name: "Bangladesh",
    flag: "🇧🇩",
    code: "BD",
    dial_code: "880",
  },
  {
    name: "Barbados",
    flag: "🇧🇧",
    code: "BB",
    dial_code: "1246",
  },
  {
    name: "Belarus",
    flag: "🇧🇾",
    code: "BY",
    dial_code: "375",
  },
  {
    name: "Belgium",
    flag: "🇧🇪",
    code: "BE",
    dial_code: "32",
  },
  {
    name: "Belize",
    flag: "🇧🇿",
    code: "BZ",
    dial_code: "501",
  },
  {
    name: "Benin",
    flag: "🇧🇯",
    code: "BJ",
    dial_code: "229",
  },
  {
    name: "Bermuda",
    flag: "🇧🇲",
    code: "BM",
    dial_code: "1441",
  },
  {
    name: "Bhutan",
    flag: "🇧🇹",
    code: "BT",
    dial_code: "975",
  },
  {
    name: "Bolivia, Plurinational State of bolivia",
    flag: "🇧🇴",
    code: "BO",
    dial_code: "591",
  },
  {
    name: "Bosnia and Herzegovina",
    flag: "🇧🇦",
    code: "BA",
    dial_code: "387",
  },
  {
    name: "Botswana",
    flag: "🇧🇼",
    code: "BW",
    dial_code: "267",
  },
  {
    name: "Bouvet Island",
    flag: "🇧🇻",
    code: "BV",
    dial_code: "47",
  },
  {
    name: "Brazil",
    flag: "🇧🇷",
    code: "BR",
    dial_code: "55",
  },
  {
    name: "British Indian Ocean Territory",
    flag: "🇮🇴",
    code: "IO",
    dial_code: "246",
  },
  {
    name: "Brunei Darussalam",
    flag: "🇧🇳",
    code: "BN",
    dial_code: "673",
  },
  {
    name: "Bulgaria",
    flag: "🇧🇬",
    code: "BG",
    dial_code: "359",
  },
  {
    name: "Burkina Faso",
    flag: "🇧🇫",
    code: "BF",
    dial_code: "226",
  },
  {
    name: "Burundi",
    flag: "🇧🇮",
    code: "BI",
    dial_code: "257",
  },
  {
    name: "Cambodia",
    flag: "🇰🇭",
    code: "KH",
    dial_code: "855",
  },
  {
    name: "Cameroon",
    flag: "🇨🇲",
    code: "CM",
    dial_code: "237",
  },
  {
    name: "Canada",
    flag: "🇨🇦",
    code: "CA",
    dial_code: "1",
  },
  {
    name: "Cape Verde",
    flag: "🇨🇻",
    code: "CV",
    dial_code: "238",
  },
  {
    name: "Cayman Islands",
    flag: "🇰🇾",
    code: "KY",
    dial_code: "345",
  },
  {
    name: "Central African Republic",
    flag: "🇨🇫",
    code: "CF",
    dial_code: "236",
  },
  {
    name: "Chad",
    flag: "🇹🇩",
    code: "TD",
    dial_code: "235",
  },
  {
    name: "Chile",
    flag: "🇨🇱",
    code: "CL",
    dial_code: "56",
  },
  {
    name: "China",
    flag: "🇨🇳",
    code: "CN",
    dial_code: "86",
  },
  {
    name: "Christmas Island",
    flag: "🇨🇽",
    code: "CX",
    dial_code: "61",
  },
  {
    name: "Cocos (Keeling) Islands",
    flag: "🇨🇨",
    code: "CC",
    dial_code: "61",
  },
  {
    name: "Colombia",
    flag: "🇨🇴",
    code: "CO",
    dial_code: "57",
  },
  {
    name: "Comoros",
    flag: "🇰🇲",
    code: "KM",
    dial_code: "269",
  },
  {
    name: "Congo",
    flag: "🇨🇬",
    code: "CG",
    dial_code: "242",
  },
  {
    name: "Congo, The Democratic Republic of the Congo",
    flag: "🇨🇩",
    code: "CD",
    dial_code: "243",
  },
  {
    name: "Cook Islands",
    flag: "🇨🇰",
    code: "CK",
    dial_code: "682",
  },
  {
    name: "Costa Rica",
    flag: "🇨🇷",
    code: "CR",
    dial_code: "506",
  },
  {
    name: "Cote d'Ivoire",
    flag: "🇨🇮",
    code: "CI",
    dial_code: "225",
  },
  {
    name: "Croatia",
    flag: "🇭🇷",
    code: "HR",
    dial_code: "385",
  },
  {
    name: "Cuba",
    flag: "🇨🇺",
    code: "CU",
    dial_code: "53",
  },
  {
    name: "Cyprus",
    flag: "🇨🇾",
    code: "CY",
    dial_code: "357",
  },
  {
    name: "Czech Republic",
    flag: "🇨🇿",
    code: "CZ",
    dial_code: "420",
  },
  {
    name: "Denmark",
    flag: "🇩🇰",
    code: "DK",
    dial_code: "45",
  },
  {
    name: "Djibouti",
    flag: "🇩🇯",
    code: "DJ",
    dial_code: "253",
  },
  {
    name: "Dominica",
    flag: "🇩🇲",
    code: "DM",
    dial_code: "1767",
  },
  {
    name: "Dominican Republic",
    flag: "🇩🇴",
    code: "DO",
    dial_code: "1849",
  },
  {
    name: "Ecuador",
    flag: "🇪🇨",
    code: "EC",
    dial_code: "593",
  },
  {
    name: "Egypt",
    flag: "🇪🇬",
    code: "EG",
    dial_code: "20",
  },
  {
    name: "El Salvador",
    flag: "🇸🇻",
    code: "SV",
    dial_code: "503",
  },
  {
    name: "Equatorial Guinea",
    flag: "🇬🇶",
    code: "GQ",
    dial_code: "240",
  },
  {
    name: "Eritrea",
    flag: "🇪🇷",
    code: "ER",
    dial_code: "291",
  },
  {
    name: "Estonia",
    flag: "🇪🇪",
    code: "EE",
    dial_code: "372",
  },
  {
    name: "Ethiopia",
    flag: "🇪🇹",
    code: "ET",
    dial_code: "251",
  },
  {
    name: "Falkland Islands (Malvinas)",
    flag: "🇫🇰",
    code: "FK",
    dial_code: "500",
  },
  {
    name: "Faroe Islands",
    flag: "🇫🇴",
    code: "FO",
    dial_code: "298",
  },
  {
    name: "Fiji",
    flag: "🇫🇯",
    code: "FJ",
    dial_code: "679",
  },
  {
    name: "Finland",
    flag: "🇫🇮",
    code: "FI",
    dial_code: "358",
  },
  {
    name: "France",
    flag: "🇫🇷",
    code: "FR",
    dial_code: "33",
  },
  {
    name: "French Guiana",
    flag: "🇬🇫",
    code: "GF",
    dial_code: "594",
  },
  {
    name: "French Polynesia",
    flag: "🇵🇫",
    code: "PF",
    dial_code: "689",
  },
  {
    name: "French Southern Territories",
    flag: "🇹🇫",
    code: "TF",
    dial_code: "262",
  },
  {
    name: "Gabon",
    flag: "🇬🇦",
    code: "GA",
    dial_code: "241",
  },
  {
    name: "Gambia",
    flag: "🇬🇲",
    code: "GM",
    dial_code: "220",
  },
  {
    name: "Georgia",
    flag: "🇬🇪",
    code: "GE",
    dial_code: "995",
  },
  {
    name: "Germany",
    flag: "🇩🇪",
    code: "DE",
    dial_code: "49",
  },
  {
    name: "Ghana",
    flag: "🇬🇭",
    code: "GH",
    dial_code: "233",
  },
  {
    name: "Gibraltar",
    flag: "🇬🇮",
    code: "GI",
    dial_code: "350",
  },
  {
    name: "Greece",
    flag: "🇬🇷",
    code: "GR",
    dial_code: "30",
  },
  {
    name: "Greenland",
    flag: "🇬🇱",
    code: "GL",
    dial_code: "299",
  },
  {
    name: "Grenada",
    flag: "🇬🇩",
    code: "GD",
    dial_code: "1473",
  },
  {
    name: "Guadeloupe",
    flag: "🇬🇵",
    code: "GP",
    dial_code: "590",
  },
  {
    name: "Guam",
    flag: "🇬🇺",
    code: "GU",
    dial_code: "1671",
  },
  {
    name: "Guatemala",
    flag: "🇬🇹",
    code: "GT",
    dial_code: "502",
  },
  {
    name: "Guernsey",
    flag: "🇬🇬",
    code: "GG",
    dial_code: "44",
  },
  {
    name: "Guinea",
    flag: "🇬🇳",
    code: "GN",
    dial_code: "224",
  },
  {
    name: "Guinea-Bissau",
    flag: "🇬🇼",
    code: "GW",
    dial_code: "245",
  },
  {
    name: "Guyana",
    flag: "🇬🇾",
    code: "GY",
    dial_code: "592",
  },
  {
    name: "Haiti",
    flag: "🇭🇹",
    code: "HT",
    dial_code: "509",
  },
  {
    name: "Heard Island and Mcdonald Islands",
    flag: "🇭🇲",
    code: "HM",
    dial_code: "672",
  },
  {
    name: "Holy See (Vatican City State)",
    flag: "🇻🇦",
    code: "VA",
    dial_code: "379",
  },
  {
    name: "Honduras",
    flag: "🇭🇳",
    code: "HN",
    dial_code: "504",
  },
  {
    name: "Hong Kong",
    flag: "🇭🇰",
    code: "HK",
    dial_code: "852",
  },
  {
    name: "Hungary",
    flag: "🇭🇺",
    code: "HU",
    dial_code: "36",
  },
  {
    name: "Iceland",
    flag: "🇮🇸",
    code: "IS",
    dial_code: "354",
  },
  {
    name: "India",
    flag: "🇮🇳",
    code: "IN",
    dial_code: "91",
  },
  {
    name: "Indonesia",
    flag: "🇮🇩",
    code: "ID",
    dial_code: "62",
  },
  {
    name: "Iran, Islamic Republic of Persian Gulf",
    flag: "🇮🇷",
    code: "IR",
    dial_code: "98",
  },
  {
    name: "Iraq",
    flag: "🇮🇶",
    code: "IQ",
    dial_code: "964",
  },
  {
    name: "Ireland",
    flag: "🇮🇪",
    code: "IE",
    dial_code: "353",
  },
  {
    name: "Isle of Man",
    flag: "🇮🇲",
    code: "IM",
    dial_code: "44",
  },
  {
    name: "Israel",
    flag: "🇮🇱",
    code: "IL",
    dial_code: "972",
  },
  {
    name: "Italy",
    flag: "🇮🇹",
    code: "IT",
    dial_code: "39",
  },
  {
    name: "Jamaica",
    flag: "🇯🇲",
    code: "JM",
    dial_code: "1876",
  },
  {
    name: "Japan",
    flag: "🇯🇵",
    code: "JP",
    dial_code: "81",
  },
  {
    name: "Jersey",
    flag: "🇯🇪",
    code: "JE",
    dial_code: "44",
  },
  {
    name: "Jordan",
    flag: "🇯🇴",
    code: "JO",
    dial_code: "962",
  },
  {
    name: "Kazakhstan",
    flag: "🇰🇿",
    code: "KZ",
    dial_code: "7",
  },
  {
    name: "Kenya",
    flag: "🇰🇪",
    code: "KE",
    dial_code: "254",
  },
  {
    name: "Kiribati",
    flag: "🇰🇮",
    code: "KI",
    dial_code: "686",
  },
  {
    name: "Korea, Democratic People's Republic of Korea",
    flag: "🇰🇵",
    code: "KP",
    dial_code: "850",
  },
  {
    name: "Korea, Republic of South Korea",
    flag: "🇰🇷",
    code: "KR",
    dial_code: "82",
  },
  {
    name: "Kosovo",
    flag: "🇽🇰",
    code: "XK",
    dial_code: "383",
  },
  {
    name: "Kuwait",
    flag: "🇰🇼",
    code: "KW",
    dial_code: "965",
  },
  {
    name: "Kyrgyzstan",
    flag: "🇰🇬",
    code: "KG",
    dial_code: "996",
  },
  {
    name: "Laos",
    flag: "🇱🇦",
    code: "LA",
    dial_code: "856",
  },
  {
    name: "Latvia",
    flag: "🇱🇻",
    code: "LV",
    dial_code: "371",
  },
  {
    name: "Lebanon",
    flag: "🇱🇧",
    code: "LB",
    dial_code: "961",
  },
  {
    name: "Lesotho",
    flag: "🇱🇸",
    code: "LS",
    dial_code: "266",
  },
  {
    name: "Liberia",
    flag: "🇱🇷",
    code: "LR",
    dial_code: "231",
  },
  {
    name: "Libyan Arab Jamahiriya",
    flag: "🇱🇾",
    code: "LY",
    dial_code: "218",
  },
  {
    name: "Liechtenstein",
    flag: "🇱🇮",
    code: "LI",
    dial_code: "423",
  },
  {
    name: "Lithuania",
    flag: "🇱🇹",
    code: "LT",
    dial_code: "370",
  },
  {
    name: "Luxembourg",
    flag: "🇱🇺",
    code: "LU",
    dial_code: "352",
  },
  {
    name: "Macao",
    flag: "🇲🇴",
    code: "MO",
    dial_code: "853",
  },
  {
    name: "Macedonia",
    flag: "🇲🇰",
    code: "MK",
    dial_code: "389",
  },
  {
    name: "Madagascar",
    flag: "🇲🇬",
    code: "MG",
    dial_code: "261",
  },
  {
    name: "Malawi",
    flag: "🇲🇼",
    code: "MW",
    dial_code: "265",
  },
  {
    name: "Malaysia",
    flag: "🇲🇾",
    code: "MY",
    dial_code: "60",
  },
  {
    name: "Maldives",
    flag: "🇲🇻",
    code: "MV",
    dial_code: "960",
  },
  {
    name: "Mali",
    flag: "🇲🇱",
    code: "ML",
    dial_code: "223",
  },
  {
    name: "Malta",
    flag: "🇲🇹",
    code: "MT",
    dial_code: "356",
  },
  {
    name: "Marshall Islands",
    flag: "🇲🇭",
    code: "MH",
    dial_code: "692",
  },
  {
    name: "Martinique",
    flag: "🇲🇶",
    code: "MQ",
    dial_code: "596",
  },
  {
    name: "Mauritania",
    flag: "🇲🇷",
    code: "MR",
    dial_code: "222",
  },
  {
    name: "Mauritius",
    flag: "🇲🇺",
    code: "MU",
    dial_code: "230",
  },
  {
    name: "Mayotte",
    flag: "🇾🇹",
    code: "YT",
    dial_code: "262",
  },
  {
    name: "Mexico",
    flag: "🇲🇽",
    code: "MX",
    dial_code: "52",
  },
  {
    name: "Micronesia, Federated States of Micronesia",
    flag: "🇫🇲",
    code: "FM",
    dial_code: "691",
  },
  {
    name: "Moldova",
    flag: "🇲🇩",
    code: "MD",
    dial_code: "373",
  },
  {
    name: "Monaco",
    flag: "🇲🇨",
    code: "MC",
    dial_code: "377",
  },
  {
    name: "Mongolia",
    flag: "🇲🇳",
    code: "MN",
    dial_code: "976",
  },
  {
    name: "Montenegro",
    flag: "🇲🇪",
    code: "ME",
    dial_code: "382",
  },
  {
    name: "Montserrat",
    flag: "🇲🇸",
    code: "MS",
    dial_code: "1664",
  },
  {
    name: "Morocco",
    flag: "🇲🇦",
    code: "MA",
    dial_code: "212",
  },
  {
    name: "Mozambique",
    flag: "🇲🇿",
    code: "MZ",
    dial_code: "258",
  },
  {
    name: "Myanmar",
    flag: "🇲🇲",
    code: "MM",
    dial_code: "95",
  },
  {
    name: "Namibia",
    flag: "🇳🇦",
    code: "NA",
    dial_code: "264",
  },
  {
    name: "Nauru",
    flag: "🇳🇷",
    code: "NR",
    dial_code: "674",
  },
  {
    name: "Nepal",
    flag: "🇳🇵",
    code: "NP",
    dial_code: "977",
  },
  {
    name: "Netherlands",
    flag: "🇳🇱",
    code: "NL",
    dial_code: "31",
  },
  {
    name: "Netherlands Antilles",
    flag: "",
    code: "AN",
    dial_code: "599",
  },
  {
    name: "New Caledonia",
    flag: "🇳🇨",
    code: "NC",
    dial_code: "687",
  },
  {
    name: "New Zealand",
    flag: "🇳🇿",
    code: "NZ",
    dial_code: "64",
  },
  {
    name: "Nicaragua",
    flag: "🇳🇮",
    code: "NI",
    dial_code: "505",
  },
  {
    name: "Niger",
    flag: "🇳🇪",
    code: "NE",
    dial_code: "227",
  },
  {
    name: "Nigeria",
    flag: "🇳🇬",
    code: "NG",
    dial_code: "234",
  },
  {
    name: "Niue",
    flag: "🇳🇺",
    code: "NU",
    dial_code: "683",
  },
  {
    name: "Norfolk Island",
    flag: "🇳🇫",
    code: "NF",
    dial_code: "672",
  },
  {
    name: "Northern Mariana Islands",
    flag: "🇲🇵",
    code: "MP",
    dial_code: "1670",
  },
  {
    name: "Norway",
    flag: "🇳🇴",
    code: "NO",
    dial_code: "47",
  },
  {
    name: "Oman",
    flag: "🇴🇲",
    code: "OM",
    dial_code: "968",
  },
  {
    name: "Pakistan",
    flag: "🇵🇰",
    code: "PK",
    dial_code: "92",
  },
  {
    name: "Palau",
    flag: "🇵🇼",
    code: "PW",
    dial_code: "680",
  },
  {
    name: "Palestinian Territory, Occupied",
    flag: "🇵🇸",
    code: "PS",
    dial_code: "970",
  },
  {
    name: "Panama",
    flag: "🇵🇦",
    code: "PA",
    dial_code: "507",
  },
  {
    name: "Papua New Guinea",
    flag: "🇵🇬",
    code: "PG",
    dial_code: "675",
  },
  {
    name: "Paraguay",
    flag: "🇵🇾",
    code: "PY",
    dial_code: "595",
  },
  {
    name: "Peru",
    flag: "🇵🇪",
    code: "PE",
    dial_code: "51",
  },
  {
    name: "Philippines",
    flag: "🇵🇭",
    code: "PH",
    dial_code: "63",
  },
  {
    name: "Pitcairn",
    flag: "🇵🇳",
    code: "PN",
    dial_code: "64",
  },
  {
    name: "Poland",
    flag: "🇵🇱",
    code: "PL",
    dial_code: "48",
  },
  {
    name: "Portugal",
    flag: "🇵🇹",
    code: "PT",
    dial_code: "351",
  },
  {
    name: "Puerto Rico",
    flag: "🇵🇷",
    code: "PR",
    dial_code: "1939",
  },
  {
    name: "Qatar",
    flag: "🇶🇦",
    code: "QA",
    dial_code: "974",
  },
  {
    name: "Romania",
    flag: "🇷🇴",
    code: "RO",
    dial_code: "40",
  },
  {
    name: "Russia",
    flag: "🇷🇺",
    code: "RU",
    dial_code: "7",
  },
  {
    name: "Rwanda",
    flag: "🇷🇼",
    code: "RW",
    dial_code: "250",
  },
  {
    name: "Reunion",
    flag: "🇷🇪",
    code: "RE",
    dial_code: "262",
  },
  {
    name: "Saint Barthelemy",
    flag: "🇧🇱",
    code: "BL",
    dial_code: "590",
  },
  {
    name: "Saint Helena, Ascension and Tristan Da Cunha",
    flag: "🇸🇭",
    code: "SH",
    dial_code: "290",
  },
  {
    name: "Saint Kitts and Nevis",
    flag: "🇰🇳",
    code: "KN",
    dial_code: "1869",
  },
  {
    name: "Saint Lucia",
    flag: "🇱🇨",
    code: "LC",
    dial_code: "1758",
  },
  {
    name: "Saint Martin",
    flag: "🇲🇫",
    code: "MF",
    dial_code: "590",
  },
  {
    name: "Saint Pierre and Miquelon",
    flag: "🇵🇲",
    code: "PM",
    dial_code: "508",
  },
  {
    name: "Saint Vincent and the Grenadines",
    flag: "🇻🇨",
    code: "VC",
    dial_code: "1784",
  },
  {
    name: "Samoa",
    flag: "🇼🇸",
    code: "WS",
    dial_code: "685",
  },
  {
    name: "San Marino",
    flag: "🇸🇲",
    code: "SM",
    dial_code: "378",
  },
  {
    name: "Sao Tome and Principe",
    flag: "🇸🇹",
    code: "ST",
    dial_code: "239",
  },
  {
    name: "Saudi Arabia",
    flag: "🇸🇦",
    code: "SA",
    dial_code: "966",
  },
  {
    name: "Senegal",
    flag: "🇸🇳",
    code: "SN",
    dial_code: "221",
  },
  {
    name: "Serbia",
    flag: "🇷🇸",
    code: "RS",
    dial_code: "381",
  },
  {
    name: "Seychelles",
    flag: "🇸🇨",
    code: "SC",
    dial_code: "248",
  },
  {
    name: "Sierra Leone",
    flag: "🇸🇱",
    code: "SL",
    dial_code: "232",
  },
  {
    name: "Singapore",
    flag: "🇸🇬",
    code: "SG",
    dial_code: "65",
  },
  {
    name: "Slovakia",
    flag: "🇸🇰",
    code: "SK",
    dial_code: "421",
  },
  {
    name: "Slovenia",
    flag: "🇸🇮",
    code: "SI",
    dial_code: "386",
  },
  {
    name: "Solomon Islands",
    flag: "🇸🇧",
    code: "SB",
    dial_code: "677",
  },
  {
    name: "Somalia",
    flag: "🇸🇴",
    code: "SO",
    dial_code: "252",
  },
  {
    name: "South Africa",
    flag: "🇿🇦",
    code: "ZA",
    dial_code: "27",
  },
  {
    name: "South Sudan",
    flag: "🇸🇸",
    code: "SS",
    dial_code: "211",
  },
  {
    name: "South Georgia and the South Sandwich Islands",
    flag: "🇬🇸",
    code: "GS",
    dial_code: "500",
  },
  {
    name: "Spain",
    flag: "🇪🇸",
    code: "ES",
    dial_code: "34",
  },
  {
    name: "Sri Lanka",
    flag: "🇱🇰",
    code: "LK",
    dial_code: "94",
  },
  {
    name: "Sudan",
    flag: "🇸🇩",
    code: "SD",
    dial_code: "249",
  },
  {
    name: "Suriname",
    flag: "🇸🇷",
    code: "SR",
    dial_code: "597",
  },
  {
    name: "Svalbard and Jan Mayen",
    flag: "🇸🇯",
    code: "SJ",
    dial_code: "47",
  },
  {
    name: "Eswatini",
    flag: "🇸🇿",
    code: "SZ",
    dial_code: "268",
  },
  {
    name: "Sweden",
    flag: "🇸🇪",
    code: "SE",
    dial_code: "46",
  },
  {
    name: "Switzerland",
    flag: "🇨🇭",
    code: "CH",
    dial_code: "41",
  },
  {
    name: "Syrian Arab Republic",
    flag: "🇸🇾",
    code: "SY",
    dial_code: "963",
  },
  {
    name: "Taiwan",
    flag: "🇹🇼",
    code: "TW",
    dial_code: "886",
  },
  {
    name: "Tajikistan",
    flag: "🇹🇯",
    code: "TJ",
    dial_code: "992",
  },
  {
    name: "Tanzania, United Republic of Tanzania",
    flag: "🇹🇿",
    code: "TZ",
    dial_code: "255",
  },
  {
    name: "Thailand",
    flag: "🇹🇭",
    code: "TH",
    dial_code: "66",
  },
  {
    name: "Timor-Leste",
    flag: "🇹🇱",
    code: "TL",
    dial_code: "670",
  },
  {
    name: "Togo",
    flag: "🇹🇬",
    code: "TG",
    dial_code: "228",
  },
  {
    name: "Tokelau",
    flag: "🇹🇰",
    code: "TK",
    dial_code: "690",
  },
  {
    name: "Tonga",
    flag: "🇹🇴",
    code: "TO",
    dial_code: "676",
  },
  {
    name: "Trinidad and Tobago",
    flag: "🇹🇹",
    code: "TT",
    dial_code: "1868",
  },
  {
    name: "Tunisia",
    flag: "🇹🇳",
    code: "TN",
    dial_code: "216",
  },
  {
    name: "Turkey",
    flag: "🇹🇷",
    code: "TR",
    dial_code: "90",
  },
  {
    name: "Turkmenistan",
    flag: "🇹🇲",
    code: "TM",
    dial_code: "993",
  },
  {
    name: "Turks and Caicos Islands",
    flag: "🇹🇨",
    code: "TC",
    dial_code: "1649",
  },
  {
    name: "Tuvalu",
    flag: "🇹🇻",
    code: "TV",
    dial_code: "688",
  },
  {
    name: "Uganda",
    flag: "🇺🇬",
    code: "UG",
    dial_code: "256",
  },
  {
    name: "Ukraine",
    flag: "🇺🇦",
    code: "UA",
    dial_code: "380",
  },
  {
    name: "United Arab Emirates",
    flag: "🇦🇪",
    code: "AE",
    dial_code: "971",
  },
  {
    name: "United Kingdom",
    flag: "🇬🇧",
    code: "GB",
    dial_code: "44",
  },
  {
    name: "United States",
    flag: "🇺🇸",
    code: "US",
    dial_code: "1",
  },
  {
    name: "Uruguay",
    flag: "🇺🇾",
    code: "UY",
    dial_code: "598",
  },
  {
    name: "Uzbekistan",
    flag: "🇺🇿",
    code: "UZ",
    dial_code: "998",
  },
  {
    name: "Vanuatu",
    flag: "🇻🇺",
    code: "VU",
    dial_code: "678",
  },
  {
    name: "Venezuela, Bolivarian Republic of Venezuela",
    flag: "🇻🇪",
    code: "VE",
    dial_code: "58",
  },
  {
    name: "Vietnam",
    flag: "🇻🇳",
    code: "VN",
    dial_code: "84",
  },
  {
    name: "Virgin Islands, British",
    flag: "🇻🇬",
    code: "VG",
    dial_code: "1284",
  },
  {
    name: "Virgin Islands, U.S.",
    flag: "🇻🇮",
    code: "VI",
    dial_code: "1340",
  },
  {
    name: "Wallis and Futuna",
    flag: "🇼🇫",
    code: "WF",
    dial_code: "681",
  },
  {
    name: "Yemen",
    flag: "🇾🇪",
    code: "YE",
    dial_code: "967",
  },
  {
    name: "Zambia",
    flag: "🇿🇲",
    code: "ZM",
    dial_code: "260",
  },
  {
    name: "Zimbabwe",
    flag: "🇿🇼",
    code: "ZW",
    dial_code: "263",
  },
];

export const mobileViewBreakPoint = 767;

export const entityNameEnum = {
  PRODUCTS: 'PRODUCTS',
  PRODUCT_BATCHES: 'PRODUCT_BATCHES',
  RESERVED_STOCK: 'RESERVED_STOCK',
  PRICE_LIST: 'PRICE_LIST',
  PRODUCT_GROUP: 'PRODUCT_GROUP',
  STOCK_LEVEL_TRACKING: 'STOCK_LEVEL_TRACKING',
  STOCK_ADJUSTMENT: 'STOCK_ADJUSTMENT',
  STOCK_TRANSFER: 'STOCK_TRANSFER',
  DELIVERY_CHALLAN: 'DELIVERY_CHALLAN',
  CUSTOMER: 'CUSTOMER',
  SALES_ORDER: 'SALES_ORDER',
  SALES_ESTIMATE: 'SALES_ESTIMATE',
  PACKING_SLIP: 'PACKING_SLIP',
  INVOICES: 'INVOICES',
  CONSUMPTION_ORDER: 'CONSUMPTION_ORDER',
  CREDIT_NOTE: 'CREDIT_NOTE',
  RETURN_SLIP: 'RETURN_SLIP',
  BILL_OF_MATERIAL: 'BILL_OF_MATERIAL',
  MANUFACTURING_ORDER: 'MANUFACTURING_ORDER',
  MANUAL_ENTRY: 'MANUAL_ENTRY',
  JOB_CARDS: 'JOB_CARDS',
  MATERIAL_PLANNING: 'MATERIAL_PLANNING',
  MACHINES: 'MACHINES',
  VENDORS: 'VENDORS',
  PURCHASE_REQUEST: 'PURCHASE_REQUEST',
  PURCHASE_INDENT: 'PURCHASE_INDENT',
  REQUEST_FOR_QUOTATION: 'REQUEST_FOR_QUOTATION',
  PURCHASE_ORDER: 'PURCHASE_ORDER',
  GOOD_RECEIVING_NOTE: 'GOOD_RECEIVING_NOTE',
  ACCOUNT_PAYABLE_INVOICE: 'ACCOUNT_PAYABLE_INVOICE',
  DEBIT_NOTE: 'DEBIT_NOTE',
  EXPENSES: 'EXPENSES',
  QUALITY_RULES: 'QUALITY_RULES',
  QUALITY_CHECKS: 'QUALITY_CHECKS',
  SALES_DASHBOARD: 'SALES_DASHBOARD',
  PURCHASE_DASHBOARD: 'PURCHASE_DASHBOARD',
  INVENTORY_DASHBOARD: 'INVENTORY_DASHBOARD',
  PRODUCTION_DASHBOARD: 'PRODUCTION_DASHBOARD',
  INCOMING_PAYMENTS: 'INCOMING_PAYMENTS',
  OUTGOING_PAYMENTS: 'OUTGOING_PAYMENTS',
  E_WAY_BILL: 'E_WAY_BILL',
  BATCH_RESERVATION: 'BATCH_RESERVATION',
  PRICE_LIST: 'PRICE_LIST',
  GATE_DOCUMENT: 'GATE_DOCUMENT',
  SHIFTS: 'SHIFTS',
  WORK_CENTER: 'WORK_CENTER',
  OPERATORS: 'OPERATORS',
}


export const filterTypeEnums = {
  STRING: 'string',
  NUMBER: 'number',
  BOOLEAN: 'boolean',
  OBJECT: 'object',
  ARRAY: 'array',
  DATE: 'date',
  DATE_RANGE: 'date_range',
};

export const expiryStatusDatePreset = {
  ALL_PURCHASE_ORDER: 'All Purchase Orders',
  EXPIRED: 'Expired',
  NOT_EXPIRED: 'Not Expired',
  EXPIRES_TODAY: 'Expires Today',
  EXPIRES_TOMORROW: 'Expires Tomorrow',
  EXPIRES_THIS_WEEK: 'Expires This Week',
  EXPIRES_NEXT_WEEK: 'Expires Next Week',
}

export const dateRangePresets = {
  TODAY: 'Today',
  YESTERDAY: 'Yesterday',
  THIS_WEEK: 'This Week',
  THIS_MONTH: 'This Month',
  LAST_MONTH: 'Last Month',
  THIS_QUARTER: 'This Quarter',
  LAST_QUARTER: 'Last Quarter',
  THIS_YEAR: 'This Year',
  LAST_YEAR: 'Last Year',
  CUSTOM: 'Custom',
  ...expiryStatusDatePreset,
};

export const dateRangePresetsValues = (presetKey) => {

  const getValue = (key) => {
    switch (key) {
      case dateRangePresets.TODAY:
        return [dayjs(dayjs().utc().subtract(0, 'days').startOf('day')
          .valueOf()),
        dayjs(dayjs().utc().subtract(1, 'days').endOf('day')
          .valueOf())]

      case dateRangePresets.YESTERDAY:
        return [dayjs(dayjs().utc().subtract(1, 'days').startOf('day')
          .valueOf()),
        dayjs(dayjs().utc().subtract(2, 'days').endOf('day')
          .valueOf())]

      case dateRangePresets.THIS_WEEK:
        return [dayjs(dayjs().utc().subtract(1, 'days').startOf('week')
          .valueOf()),
        dayjs(dayjs().utc().subtract(1, 'days').endOf('week')
          .subtract(1, 'day')
          .valueOf())]

      case dateRangePresets.THIS_MONTH:
        return [dayjs(dayjs().utc().subtract(1, 'days').startOf('month')
          .valueOf()),
        dayjs(dayjs().utc().subtract(1, 'days').endOf('month')
          .subtract(1, 'day')
          .valueOf())]

      case dateRangePresets.LAST_MONTH:
        return [dayjs(dayjs().utc().subtract(1, 'months').startOf('month')
          .valueOf()),
        dayjs(dayjs().utc().subtract(1, 'months').endOf('month')
          .subtract(1, 'day')
          .valueOf())]

      case dateRangePresets.THIS_QUARTER:
        return [dayjs(dayjs().utc().quarter(dayjs().utc().quarter()).startOf('quarter')
          .valueOf()),
        dayjs(dayjs().utc().endOf('quarter').subtract(1, 'day')
          .valueOf())]

      case dateRangePresets.LAST_QUARTER:
        return [dayjs(dayjs().utc().quarter(dayjs().utc().quarter()).subtract(1, 'quarter')
          .startOf('quarter')
          .valueOf()),
        dayjs(dayjs().utc().quarter(dayjs().utc().quarter()).subtract(1, 'quarter')
          .endOf('quarter')
          .subtract(1, 'day')
          .valueOf())]

      case dateRangePresets.THIS_YEAR:
        return [dayjs(dayjs().utc().startOf('years').valueOf()),
        dayjs(dayjs().utc().endOf('years').subtract(1, 'day')
          .valueOf())]

      case dateRangePresets.LAST_YEAR:
        return [dayjs(dayjs().utc().subtract(1, 'years').startOf('years')
          .valueOf()),
        dayjs(dayjs().utc().subtract(1, 'years').endOf('years')
          .subtract(1, 'day')
          .valueOf())]

      case dateRangePresets.ALL_PURCHASE_ORDER:
        return ['', '']

      case dateRangePresets.EXPIRED:
        return ['', dayjs(dayjs().utc().startOf('day').valueOf()).valueOf()]

      case dateRangePresets.NOT_EXPIRED:
        return [dayjs(dayjs().utc().startOf('day').valueOf()).valueOf(),
        dayjs(dayjs('2099-01-01').utc().endOf('day').valueOf()).valueOf()]

      case dateRangePresets.EXPIRES_TODAY:
        return [dayjs(dayjs().utc().startOf('day').valueOf()).valueOf(), dayjs(dayjs().utc().endOf('day').valueOf()).valueOf()]

      case dateRangePresets.EXPIRES_TOMORROW:
        return [dayjs(dayjs().utc().add(1, 'days').startOf('day')
          .valueOf()).valueOf(), dayjs(dayjs().utc().add(1, 'days').endOf('day')
            .valueOf()).valueOf()]

      case dateRangePresets.EXPIRES_THIS_WEEK:
        return [dayjs(dayjs().utc().startOf('week').valueOf()).valueOf(),
        dayjs(dayjs().utc().endOf('week').valueOf()).valueOf()]

      case dateRangePresets.EXPIRES_NEXT_WEEK:
        return [dayjs(dayjs().utc().add(1, 'week').startOf('week')
          .valueOf()).valueOf(),
        dayjs(dayjs().utc().add(1, 'week').endOf('week')
          .valueOf()).valueOf()]

      default:
        return []
    }
  }
  const [start, end] = getValue(presetKey);

  if (Object.values(expiryStatusDatePreset).includes(presetKey)) {
    return [start, end].toString()
  } else {

    const _start = start.startOf('day');
    const _end = end.endOf('day');

    const startDate = dayjs(_start)?.startOf('day').valueOf()
    const endDate = dayjs(_end)?.endOf('day').valueOf()

    return [startDate, endDate];
  }
};