/* eslint-disable react/prop-types */
// FiltersOnlyComponent.jsx
import React from 'react';
import { Select, Switch } from 'antd';
import Helpers from '@Apis/helpers';
import CategoryFilterNew from '@Components/Common/CategoryFilterNew';
import './style.scss';
import SelectDepartment from '../../../../Common/SelectDepartment';
import H3Text from '../../../../../uilib/h3Text';
import PRZSelect from '../../../../Common/UI/PRZSelect';

const { Option } = Select;

const ProductFilterDrawer = ({
  user,
  selectedStatus,
  setSelectedStatus,
  selectedType,
  setSelectedType,
  selectedCategory,
  setSelectedCategory,
  marketplaceProduct,
  setMarketplaceProduct,
  setCurrentPage,
  setSelectedDepartment,
  selectedDepartment,
  depFilterDisableCase,
  openAdvanceProductFiltersDrawer,
  setOpenAdvanceProductFiltersDrawer,
}) => {

  const stockStatusOptions = [
  { value: '', label: 'All Status' },
  { value: 'WELL_STOCKED', label: 'Well Stocked' },
  { value: 'RUNNING_LOW', label: 'Running Low' },
  { value: 'OUT_OF_STOCK', label: 'Out of Stock' },
];

const productTypeOptions = [
  { value: '', label: 'All Types' },
  { value: 'STORABLE', label: 'Storable' },
  { value: 'NON_STORABLE', label: 'Non Storable' },
  { value: 'BUNDLE', label: 'Bundle' },
  { value: 'SERVICE', label: 'Service' },
];

  return (
    <div className="filter__container-wrapper">

      <div className="filter__container inventory__header__section-status filter__container-section">
        <div className="filter__title">Stock Status</div>
        <PRZSelect
          value={selectedStatus}
          onChange={(value) => {
            setSelectedStatus(value);
            setCurrentPage(1); // Reset to page 1
          }}
          placeholder="Stock Status"
        options={stockStatusOptions}
        />
      </div>

      <div className="filter__container inventory__header__section-status filter__container-section">
        <div className="filter__title">Product Type</div>
        <PRZSelect
          value={selectedType}
          onChange={(value) => {
            setSelectedType(value);
            setCurrentPage(1); // Reset to page 1
          }}
          placeholder="Product Type"
          options={productTypeOptions}
        />
      </div>

      <div className="filter__container inventory__header__section-status filter__container-section">
        <div className="filter__title">Product Category</div>
        <CategoryFilterNew
          selectedCategory={selectedCategory}
          containerClassName="section-status"
          hideTitle
          placeholder="Product Category"
          style={{
            width: '100%',
            height: '28px',
            border: 'none',
            borderRadius: '3px',
          }}
          onChange={(value) => setSelectedCategory(value)}
          allowClear
        />
      </div>
      <div className="filter__container inventory__header__section-status filter__container-section">
        <div className="filter__title">Select Department</div>
        <SelectDepartment
          hideTitle
          selectedDepartment={Number(selectedDepartment) || ''}
          noDropdownAlign
          onChange={(value) => {
            setSelectedDepartment(value?.department_id.toString() || 'all');
            setCurrentPage(1);
          }}
          tenantId={user.tenant_info?.tenant_id}
          disabled={depFilterDisableCase}
        />
      </div>

      <div className="filter__container inventory__header__section-status filter__container-section">
        <div className="filter__title">Marketplace Products</div>
        <Switch
          checked={marketplaceProduct}
          onChange={() => setMarketplaceProduct(!marketplaceProduct)}
        />
      </div>

      <div className="advanced-filter__container" onClick={() => setOpenAdvanceProductFiltersDrawer(true)}>
        <H3Text
          text={(
            <div className="user-menu__title-wrapper">
              Advanced Filters
              {/* &nbsp;
              <span className="new__tag">New</span> */}
            </div>
          )}
          className="action-button-text"
        />
      </div>

    </div>
  );
};

export default ProductFilterDrawer;
