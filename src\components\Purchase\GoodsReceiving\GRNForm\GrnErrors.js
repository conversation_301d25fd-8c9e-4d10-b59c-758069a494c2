import { message } from 'antd';
import dayjs from 'dayjs';
import FormHelpers from '../../../../helpers/FormHelpers';

const ERROR_MESSAGES = {
  requiredField: (fieldName) => `Error in ${fieldName} field. Select/Enter a valid value then proceed.`,
};

// Utility to validate document-level errors
const validateDocLevelFields = ({
  selectedSeller, invoiceNumber, selectedPoForGrn, cfGoodReceivingNotesDoc, grnDate,
  grnTypeValue, selectedPoValue, shippingAddress, grnId, ewayBillNumber, checkedRecipients,
  data, ewayBillList, vendorAddress, toRecipients, user, invoiceDate, selectedTenant, grnNumber,
}) => {
  const errors = [];
  const allowGRNDateBeforeInvoiceDate = !user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.allow_grn_date_before_invoice_date;

  const LineTotal = (tableData, typeValue) => {
    let totalAmount = 0;

    if (tableData?.length > 0) {
      for (let i = 0; i < tableData.length; i++) {
        const quantityToUse = typeValue === 'ADHOC'
          ? tableData[i]?.quantity
          : tableData[i]?.received_qty;

        if (quantityToUse && tableData[i]?.offer_price) {
          totalAmount += quantityToUse * tableData[i].offer_price;
        }
      }
    }

    return { totalAmount };
  };

  if (!grnNumber) {
    errors.push({
      name: "GRN Number",
      value: "",
      message: ERROR_MESSAGES.requiredField("GRN Number"),
    });
  }

  if (!selectedSeller && !grnId && !selectedPoForGrn) {
    errors.push({
      name: "Customer",
      value: "",
      message: ERROR_MESSAGES.requiredField("Select Vendor"),
    });
  }

  if (!vendorAddress && !grnId && !selectedPoForGrn) {
    errors.push({
      name: "Vendor's Address",
      value: "",
      message: ERROR_MESSAGES.requiredField("Vendor's Address"),
    });
  }

  if (grnTypeValue === 'Purchase Order' && !selectedPoValue && !grnId && !selectedPoForGrn) {
    errors.push({
      name: "Purchase Order",
      value: "",
      message: ERROR_MESSAGES.requiredField("Select Order"),
    });
  }
  if (grnTypeValue === 'ADHOC' && !selectedTenant && !grnId && !selectedPoForGrn) {
    errors.push({
      name: "Location",
      value: "",
      message: ERROR_MESSAGES.requiredField("Select Location"),
    });
  }

  if (grnTypeValue === 'Purchase Order' && !selectedPoValue && !selectedPoForGrn?.po_id) {
    if (!shippingAddress) {
      errors.push({
        name: "Delivery Address",
        value: "",
        message: ERROR_MESSAGES.requiredField("Delivery Address"),
      });
    }
  }

  if (
    !invoiceNumber
    && user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.grn_invoice_number_mandatory
  ) {
    errors.push({
      name: "Invoice Number",
      value: "",
      message: ERROR_MESSAGES.requiredField("Vendor Invoice#"),
    });
  }

  if (!grnDate) {
    errors.push({
      name: 'GRN Date',
      value: '',
      message: ERROR_MESSAGES.requiredField('GRN Date'),
    });
  }
  if (allowGRNDateBeforeInvoiceDate
    && dayjs(grnDate).isValid()
    && dayjs(invoiceDate).isValid()
    && dayjs(grnDate).isBefore(dayjs(invoiceDate))) {
    errors.push({
      name: 'GRN Date',
      value: '',
      message: 'Error in The GRN date cannot be earlier than the invoice date. Please verify the entered dates.',
    });
  }

  if (
    user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.is_e_way_bill_mandatory &&
    LineTotal(data, grnTypeValue).totalAmount >= 50000
  ) {
    if (!ewayBillNumber) {
      errors.push({
        name: "e-Way Bill number",
        value: "",
        message: ERROR_MESSAGES.requiredField("e-Way Bill number"),
      });
    }

    if (!ewayBillList || (ewayBillList?.length === 0)) {
      errors.push({
        name: "e-Way Bill Attachment",
        value: "",
        message: ERROR_MESSAGES.requiredField("e-Way Bill Attachment"),
      });
    }
  }

  if (checkedRecipients) {
    if (!toRecipients || (toRecipients?.length === 0)) {
      errors.push({
        name: "Recipient Email",
        value: "",
        message: ERROR_MESSAGES.requiredField("Recipient Email"),
      });
    }
  }

  if (cfGoodReceivingNotesDoc) {
    cfGoodReceivingNotesDoc.forEach((field) => {
      if (field?.isRequired && field?.isActive) {
        errors.push({
          name: field?.fieldName,
          value: field?.fieldValue,
          message: ERROR_MESSAGES.requiredField(field?.fieldName),
        });
      }
    });
  }

  return errors;
};

// Utility to validate line-level errors
const validateLineLevelFields = ({ data, cfGoodReceivingNotesLine, grnTypeValue, isMultipleBatchModeEnabled, isBatchValid, isApInvoiceEnabled, user }) => {
  const calculateTotalMultipleBatchQuantity = (multipleBatchData) => {
    let total = 0;
    multipleBatchData?.forEach((item) => {
      total += (Number(item?.quantity));
    });
    return total;
  };
  const errors = [];

  if (data && (grnTypeValue === 'Purchase Order')) {
    data.forEach((item) => {
      const quantity = Number(item?.quantity);
      const grnPercent = Number(item?.product_sku_info?.grn_over_flow_percent || 0);
      const overFlowQuantity = Math.floor(((grnPercent / 100) * quantity) + quantity);
      const enteredQty = Number(item.received_qty);
      function checkOverflow() {
        if (user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.flexible_qty_wrt_po) {
          if (grnPercent > 0) {
            if ((overFlowQuantity) - Number(item.total_received_qty) - Number(item.received_qty) < 0) {
              return true;
            }
          } else if (grnPercent === 0 || grnPercent === null) {
            if ((overFlowQuantity - Number(item.total_received_qty) - Number(item.received_qty)) < 0) {
              return false;
            }
          }
        } else if (grnPercent > 0) {
          if ((overFlowQuantity) - Number(item.total_received_qty) - Number(item.received_qty) < 0) {
            return true;
          }
        } else if (grnPercent === 0 || grnPercent === null) {
          if ((quantity - Number(item.total_received_qty) - Number(item.received_qty)) < 0) {
            return true;
          }
        }
      }
      if (checkOverflow()) {
        const allowedQtyText = grnPercent > 0
          ? `${quantity} + ${grnPercent}% = ${overFlowQuantity}`
          : `${quantity}`;
        const overflowNote = `Allowed: ${allowedQtyText}, Entered: ${enteredQty}`;

        errors.push({
          name: "GRN Quantity Exceeded",
          value: 0,
          message: ERROR_MESSAGES.requiredField(
            `Product "${item?.product_sku_info?.internal_sku_code}" has exceeded the GRN overflow limit. ${overflowNote}`
          ),
        });
      }
      if (cfGoodReceivingNotesLine) {
        item?.lineCustomFields?.filter((field) => field?.fieldName !== "Quantity" && field?.fieldName !== "Rate")?.forEach((field) => {
          if (field.isRequired && field?.isActive) {
            errors.push({
              name: field?.fieldName,
              value: field?.fieldValue,
              message: ERROR_MESSAGES.requiredField(`${field?.fieldName} for Product '${item?.product_sku_info?.internal_sku_code || ""}`),
            });
          }
        });
      }
      // batch cf error
      if (item?.selectedBatch?.custom_fields && item?.multiple_batch_info?.length === 0) {
        item?.selectedBatch?.custom_fields?.forEach((field) => {
          if (field.isRequired && field?.isActive) {
            errors.push({
              name: field?.fieldName,
              value: field?.fieldValue,
              message: ERROR_MESSAGES.requiredField(`${field?.fieldName} for Product '${item?.product_sku_info?.internal_sku_code || ""}`),
            });
          }
        });
      }

      if (!isBatchValid) {
        errors.push({
          name: "Product Batch",
          value: 0,
          message: ERROR_MESSAGES.requiredField(`Product "${item?.product_sku_info?.internal_sku_code}'s" Batch`),
        });
      }
      if (
        isApInvoiceEnabled
          ? !item?.offer_price && item?.offer_price !== 0
          : !item?.offer_price || item?.offer_price == 0
      ) {
        errors.push({
          name: "Cost Price",
          value: 0,
          message: ERROR_MESSAGES.requiredField(`Product "${item?.product_sku_info?.internal_sku_code}'s" Unit Price`),
        });
      }

      if (!item?.taxInfo) {
        errors.push({
          name: "Tax",
          value: 0,
          message: ERROR_MESSAGES.requiredField(`Product "${item?.product_sku_info?.internal_sku_code}'s" Tax`),
        });
      }

      if (Number(item?.received_qty) === 0) {
        errors.push({
          name: "Received Quantity",
          value: 0,
          message: ERROR_MESSAGES.requiredField(`Product "${item?.product_sku_info?.internal_sku_code}'s" Received`),
        });
      }

      if (item?.po_line_status?.length > 0) {

        item?.po_line_status?.forEach((status) => {
          if (status?.line_status_received_qty > status?.quantity) {
            errors.push({
              name: status?.status_name,
              value: 0,
              message: ERROR_MESSAGES.requiredField(`Product "${item?.product_sku_info?.internal_sku_code}'s" ${status?.status_name} Status`),
            });
          }
        });
      }
      if (isMultipleBatchModeEnabled && item?.received_qty !== calculateTotalMultipleBatchQuantity(item?.multiple_batch_info)) {
        errors.push({
          name: "Received Quantity Mismatch",
          value: 0,
          message: ERROR_MESSAGES.requiredField(`Product "${item?.product_sku_info?.internal_sku_code}'s" Received Quantity`),
        });
      }
    });
  }

  if (data && (grnTypeValue === 'ADHOC')) {
    data.forEach((item) => {
      if (!item?.product_sku_info) {
        errors.push({
          name: "Product",
          value: "",
          message: "Select a Product to continue",
        })
      } else {
        if (
          isApInvoiceEnabled
            ? !item?.offer_price && item?.offer_price !== 0
            : !item?.offer_price || item?.offer_price == 0
        ) {
          errors.push({
            name: "Cost Price",
            value: 0,
            message: ERROR_MESSAGES.requiredField(`Product "${item?.product_sku_info?.internal_sku_code}'s" Unit Price`),
          });
        }

        if (
          isApInvoiceEnabled &&
          ((item?.invoiceQuantity || item?.invoiceQuantity === 0)
            ? Number(item?.invoiceQuantity) <= 0
            : grnTypeValue === "ADHOC"
              ? Number(item?.quantity) <= 0
              : Number(item?.received_qty) <= 0)
        ) {
          errors.push({
            name: "Invoice Quantity",
            value: 0,
            message: ERROR_MESSAGES.requiredField(
              `Product "${item?.product_sku_info?.internal_sku_code}'s" Invoice Quantity`
            ),
          });
        }

        if (!item?.taxInfo) {
          errors.push({
            name: "Tax",
            value: 0,
            message: ERROR_MESSAGES.requiredField(`Product "${item?.product_sku_info?.internal_sku_code}'s" Tax`),
          });
        }

        if (!isBatchValid) {
          errors.push({
            name: "Product Batch",
            value: 0,
            message: ERROR_MESSAGES.requiredField(`Product "${item?.product_sku_info?.internal_sku_code}'s" Batch`),
          });
        }

        if (cfGoodReceivingNotesLine) {
          item?.lineCustomFields?.filter((field) => field?.fieldName !== "Quantity" && field?.fieldName !== "Rate")?.forEach((field) => {
            if (field.isRequired && field?.isActive) {
              errors.push({
                name: field?.fieldName,
                value: field?.fieldValue,
                message: ERROR_MESSAGES.requiredField(`${field?.fieldName} for Product '${item?.product_sku_info?.internal_sku_code || ""}`),
              });
            }
          });
        }
        // batch cf error
        if (item?.selectedBatch?.custom_fields && item?.multiple_batch_info?.length === 0) {
          item?.selectedBatch?.custom_fields?.forEach((field) => {
            if (field.isRequired && field?.isActive) {
              errors.push({
                name: field?.fieldName,
                value: field?.fieldValue,
                message: ERROR_MESSAGES.requiredField(`${field?.fieldName} for Product '${item?.product_sku_info?.internal_sku_code || ""}`),
              });
            }
          });
        }

      }

      if (Number(item?.quantity) === 0) {
        errors.push({
          name: "Received Quantity",
          value: 0,
          message: ERROR_MESSAGES.requiredField(`Product "${item?.product_sku_info?.internal_sku_code}'s" Quantity`),
        });
      }
    })
  }

  return errors;
};

// Main function to get packing slip error list
const grnErrorList = (params) => {
  const docLevelErrors = validateDocLevelFields(params);
  const lineLevelErrors = validateLineLevelFields(params);

  return FormHelpers.errorChecker({ docLevelErrors, lineLevelErrors });
};

export default grnErrorList;
